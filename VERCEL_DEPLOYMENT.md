# 🌐 Deploying Your Whale Tracker to Vercel

## 🤔 What is Ver<PERSON>?

Think of Ver<PERSON> as **"a computer in the cloud that never turns off"**. Instead of keeping your laptop running 24/7 to track whales, Vercel runs your code on their servers.

### **Why Use Vercel?**
- ✅ **24/7 Operation**: Your whale tracker runs even when your computer is off
- ✅ **Free Tier**: Generous free plan for personal projects
- ✅ **Automatic Updates**: Push code changes and they deploy automatically
- ✅ **No Maintenance**: No need to manage servers or updates
- ✅ **Global**: Fast performance worldwide

### **How It Works:**
1. You upload your whale tracker code to Vercel
2. Vercel runs it on their cloud servers 24/7
3. Your Telegram bot stays online and sends daily whale reports
4. You can still use `/check` commands anytime

## 🚀 **Step-by-Step Vercel Deployment**

### **Prerequisites**
- Your whale tracker working locally (`python main.py test` passes)
- All API keys set up in `.env` file
- Node.js installed (for Vercel CLI)

### **Step 1: Install Vercel CLI**

**Option A: Using npm (if you have Node.js)**
```bash
npm install -g vercel
```

**Option B: Download directly**
- Go to https://vercel.com/download
- Download and install Vercel CLI

### **Step 2: Prepare Your Project**

**Create Vercel Configuration File:**
```bash
# In your whale-watch directory, create vercel.json
```

I'll create this file for you:

```json
{
  "version": 2,
  "builds": [
    {
      "src": "main.py",
      "use": "@vercel/python"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "main.py"
    }
  ],
  "env": {
    "TELEGRAM_BOT_TOKEN": "@telegram_bot_token",
    "TELEGRAM_CHAT_ID": "@telegram_chat_id", 
    "ETHERSCAN_API_KEY": "@etherscan_api_key",
    "COINGECKO_API_KEY": "@coingecko_api_key",
    "GOOGLE_SHEETS_ID": "@google_sheets_id"
  },
  "functions": {
    "main.py": {
      "maxDuration": 300
    }
  }
}
```

### **Step 3: Login to Vercel**

```bash
# Login to Vercel (opens browser)
vercel login

# Follow the prompts to sign up/login
```

### **Step 4: Deploy Your Project**

```bash
# In your whale-watch directory
vercel

# Follow the prompts:
# - Set up and deploy? Y
# - Which scope? (choose your account)
# - Link to existing project? N
# - What's your project's name? whale-tracker
# - In which directory is your code located? ./
```

### **Step 5: Set Environment Variables**

After deployment, you need to add your API keys to Vercel:

```bash
# Add your environment variables
vercel env add TELEGRAM_BOT_TOKEN
# Paste your bot token when prompted

vercel env add TELEGRAM_CHAT_ID  
# Paste your chat ID when prompted

vercel env add ETHERSCAN_API_KEY
# Paste your Etherscan key when prompted

# Optional ones:
vercel env add COINGECKO_API_KEY
vercel env add GOOGLE_SHEETS_ID
```

### **Step 6: Deploy Production Version**

```bash
# Deploy to production
vercel --prod
```

## ⚠️ **Important Considerations**

### **Vercel Limitations for Long-Running Apps**

**The Challenge**: Vercel is designed for "serverless functions" that run briefly, not 24/7 bots.

**Solutions:**

**Option 1: Scheduled Functions (Recommended)**
- Convert to scheduled functions that run periodically
- Use Vercel Cron Jobs for daily whale checks
- Store state in external database

**Option 2: Alternative Platforms**
For 24/7 operation, consider:
- **Railway** (better for long-running apps)
- **Render** (has free tier for background services)
- **Heroku** (classic choice, has free tier)
- **DigitalOcean App Platform**

## 🔄 **Alternative: Railway Deployment (Recommended)**

Railway is better suited for your whale tracker:

### **Why Railway?**
- ✅ Designed for 24/7 applications
- ✅ Free tier with $5/month credit
- ✅ Easier for Python bots
- ✅ Built-in database options

### **Railway Setup:**

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login
railway login

# Deploy
railway deploy
```

## 🎯 **My Recommendation**

**For Your Whale Tracker:**

1. **Start Local**: Test everything locally first
2. **Choose Platform**: 
   - **Railway** for 24/7 operation (recommended)
   - **Vercel** for scheduled checks only
3. **Deploy**: Follow the platform-specific guide
4. **Monitor**: Check logs to ensure it's working

## 📋 **Deployment Checklist**

**Before Deploying:**
- [ ] Local testing works (`python main.py test`)
- [ ] All API keys in `.env` file
- [ ] Telegram bot responds to commands
- [ ] Google Sheets connection working (if using)

**After Deploying:**
- [ ] Environment variables set on platform
- [ ] Bot responds to Telegram commands
- [ ] Daily whale checks working
- [ ] Logs show no errors

## 🆘 **Troubleshooting Deployment**

**Common Issues:**

1. **"Environment variables not found"**
   - Make sure you added all API keys to the platform
   - Check variable names match exactly

2. **"Bot not responding"**
   - Check Telegram bot token is correct
   - Verify chat ID is correct
   - Check platform logs for errors

3. **"API rate limits"**
   - Ensure rate limiting is working
   - Check API key validity

**Debug Commands:**
```bash
# Check Vercel logs
vercel logs

# Check Railway logs  
railway logs

# Test locally first
python main.py test
```

## 🎉 **When It's Working**

You'll know deployment is successful when:
- ✅ Your Telegram bot responds to `/check`
- ✅ You receive daily whale digests
- ✅ Google Sheets gets updated (if configured)
- ✅ Platform logs show no errors

**Ready to deploy?** Start with local testing, then choose your platform! 🚀
