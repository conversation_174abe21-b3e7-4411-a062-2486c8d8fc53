"""
🤖 TELEGRAM BOT - telegram_bot.py

This is your PERSONAL PORTFOLIO ASSISTANT! This file:

1. 💬 Responds to your Telegram commands (/check, /help, /portfolio)
2. 📱 Sends you daily portfolio digests every morning
3. 🎨 Formats portfolio data into beautiful messages
4. 🔗 Adds clickable links to blockchain explorers
5. ⚡ Provides instant portfolio updates on demand

WHAT IT DOES:
- Listens for your Telegram commands 24/7
- Formats portfolio data into easy-to-read messages
- Sends daily summaries of your crypto performance
- Shows your current holdings and PnL

COMMANDS YOU CAN USE:
- /start - Welcome message
- /check - Get portfolio performance summary
- /portfolio - Show detailed portfolio breakdown
- /help - Show all available commands

This is your direct line to portfolio intelligence via Telegram!
"""

import logging
from datetime import datetime
from typing import List, Dict
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes
import config
from api_handler import APIHandler
from portfolio_calculator import get_portfolio_digest, get_portfolio_performance

# Enhanced learning features removed for portfolio tracker
ENHANCED_FEATURES_AVAILABLE = False

logger = logging.getLogger(__name__)

class PortfolioTrackerBot:
    def __init__(self):
        self.api_handler = APIHandler()
        self.application = None
        
    def format_whale_message(self, transactions: List[Dict], title: str = "🐋 Whale Alert") -> List[str]:
        """Format whale transactions into compact Telegram messages"""
        if not transactions:
            return [f"{title}\n\nNo whale transactions found in the specified period."]

        # Enhanced filtering removed for portfolio tracker
        # Use all transactions for display
        display_transactions = transactions

        # Group transactions by blockchain for compact display
        return self._format_compact_summary(display_transactions, title)
    
    def _format_single_transaction(self, tx: Dict, index: int) -> str:
        """Format a single transaction for display"""
        blockchain = tx.get('blockchain', 'Unknown')
        amount = tx.get('amount', 0)
        currency = tx.get('currency', 'Unknown')
        usd_value = tx.get('usd_value', 0)
        timestamp = tx.get('timestamp', datetime.now())
        tx_hash = tx.get('hash', 'Unknown')
        
        # Format timestamp
        time_str = timestamp.strftime("%H:%M UTC") if isinstance(timestamp, datetime) else "Unknown"
        
        # Format amount with appropriate decimals
        if currency == 'BTC':
            amount_str = f"{amount:.4f}"
        else:
            amount_str = f"{amount:.2f}"
        
        # Create blockchain explorer link
        explorer_link = self._get_explorer_link(blockchain, tx_hash)
        
        # Format the transaction
        tx_info = f"#{index} {blockchain} Transaction\n"
        tx_info += f"💰 {amount_str} {currency} (${usd_value:,.0f})\n"
        tx_info += f"⏰ {time_str}\n"
        
        if explorer_link:
            tx_info += f"🔗 [View on Explorer]({explorer_link})\n"
        else:
            tx_info += f"🔗 Hash: `{tx_hash[:16]}...`\n"
        
        tx_info += "\n"
        
        return tx_info

    def _format_compact_summary(self, transactions: List[Dict], title: str) -> List[str]:
        """Format transactions into compact summary by blockchain"""
        if not transactions:
            return [f"{title}\n\nNo whale transactions found."]

        # Group by blockchain
        by_blockchain = {}
        total_usd = 0

        for tx in transactions:
            blockchain = tx.get('blockchain', 'Unknown')
            amount = tx.get('amount', 0)
            usd_value = tx.get('usd_value', 0)
            total_usd += usd_value

            # Determine transaction type for emoji
            tx_type = tx.get('transaction_type', 'unknown')
            from_exchange = tx.get('from_exchange', 'Unknown')
            to_exchange = tx.get('to_exchange', 'Unknown')

            if tx_type == 'whale_to_exchange':
                emoji = '🚨'
                description = f"→ {to_exchange}"
            elif tx_type == 'exchange_to_whale':
                emoji = '💰'
                description = f"{from_exchange} →"
            elif tx_type == 'exchange_to_exchange':
                emoji = '🔄'
                description = "Exchange transfer"
            elif tx.get('round_amount', False):
                emoji = '🔄'
                description = "Round amount"
            else:
                emoji = '🐋'
                description = "OTC"

            if blockchain not in by_blockchain:
                by_blockchain[blockchain] = []

            # Format amount
            if blockchain == 'BTC':
                amount_str = f"{amount:.4f}"
            else:
                amount_str = f"{amount:.2f}"

            by_blockchain[blockchain].append({
                'emoji': emoji,
                'amount_str': amount_str,
                'currency': blockchain,
                'usd_value': usd_value,
                'description': description
            })

        # Build compact message
        message = f"{title}\n\n"

        for blockchain, txs in by_blockchain.items():
            if len(txs) == 1:
                tx = txs[0]
                message += f"{tx['emoji']} {blockchain}: {tx['amount_str']} {tx['currency']} (${tx['usd_value']:,.0f}) {tx['description']}\n"
            else:
                message += f"🐋 {blockchain} Activity ({len(txs)} transactions):\n"
                for tx in txs[:5]:  # Limit to 5 per blockchain
                    message += f"• {tx['emoji']} {tx['amount_str']} {tx['currency']} (${tx['usd_value']:,.0f}) {tx['description']}\n"
                if len(txs) > 5:
                    remaining_usd = sum(tx['usd_value'] for tx in txs[5:])
                    message += f"• ... and {len(txs) - 5} more (${remaining_usd:,.0f})\n"
                message += "\n"

        # Add total
        message += f"💰 Total: ${total_usd:,.0f} in whale movements"

        return [message]

    def _format_enhanced_transaction(self, tx: Dict, index: int) -> str:
        """Format a single transaction with enhanced exchange information"""
        blockchain = tx.get('blockchain', 'Unknown')
        amount = tx.get('amount', 0)
        currency = tx.get('currency', 'Unknown')
        usd_value = tx.get('usd_value', 0)
        timestamp = tx.get('timestamp', datetime.now())
        tx_hash = tx.get('hash', 'Unknown')

        # Enhanced information
        tx_type = tx.get('transaction_type', 'unknown')
        from_exchange = tx.get('from_exchange', 'Unknown')
        to_exchange = tx.get('to_exchange', 'Unknown')

        # Format timestamp
        time_str = timestamp.strftime("%H:%M UTC") if isinstance(timestamp, datetime) else "Unknown"

        # Format amount with appropriate decimals
        if currency == 'BTC':
            amount_str = f"{amount:.4f}"
        else:
            amount_str = f"{amount:.2f}"

        # Create appropriate emoji and description based on transaction type
        if tx_type == 'whale_to_exchange':
            emoji = '🚨'
            direction = f"Whale → {to_exchange}"
            context = "📤 Potential sell pressure!"
        elif tx_type == 'exchange_to_whale':
            emoji = '💰'
            direction = f"{from_exchange} → Whale"
            context = "📥 Potential accumulation!"
        elif tx_type == 'exchange_to_exchange':
            emoji = '🔄'
            direction = f"{from_exchange} → {to_exchange}"
            context = "🏦 Inter-exchange transfer"
        elif tx_type == 'whale_to_whale':
            emoji = '🐋'
            direction = "Whale → Whale"
            context = "🤝 Large OTC transfer"
        else:
            emoji = '🔍'
            direction = "Unknown → Unknown"
            context = "❓ Monitor closely"

        # Create blockchain explorer link
        explorer_link = self._get_explorer_link(blockchain, tx_hash)

        # Format the enhanced transaction
        tx_info = f"#{index} {emoji} {blockchain} {direction}\n"
        tx_info += f"💰 {amount_str} {currency} (${usd_value:,.0f})\n"
        tx_info += f"{context}\n"
        tx_info += f"⏰ {time_str}\n"

        if explorer_link:
            tx_info += f"🔗 [View on Explorer]({explorer_link})\n"
        else:
            # Truncate hash for readability
            short_hash = tx_hash[:10] + '...' if len(tx_hash) > 10 else tx_hash
            tx_info += f"🔗 {short_hash}\n"

        tx_info += "\n"

        return tx_info

    def _format_detailed_portfolio(self, portfolio_data: Dict) -> str:
        """Format detailed portfolio breakdown"""
        if not portfolio_data:
            return "❌ No portfolio data available"

        total_value = portfolio_data['total_value']
        total_invested = portfolio_data['total_invested']
        overall = portfolio_data['overall_performance']

        # Header
        message = f"""📊 *Detailed Portfolio Breakdown*

💰 *Total Value:* ${total_value:,.2f}
💵 *Total Invested:* ${total_invested:,.2f}
📈 *Overall PnL:* {overall['total_pnl_percentage']:+.1f}% (${overall['total_pnl']:+,.2f})
📊 *Daily Change:* {overall['daily_change']:+.1f}% (${overall['daily_value_change']:+,.2f})

🎯 *Holdings:*"""

        # Sort coins by current value
        coins = sorted(portfolio_data['coins'].items(),
                      key=lambda x: x[1]['current_value'], reverse=True)

        for coin, perf in coins:
            if perf['current_value'] < 1:  # Skip dust
                continue

            pnl_emoji = "📈" if perf['pnl_percentage'] >= 0 else "📉"
            daily_emoji = "📈" if perf['daily_change'] >= 0 else "📉"

            message += f"""

*{coin}:* {perf['balance']:.6f} = ${perf['current_value']:,.2f}
• Avg Cost: ${perf['avg_cost']:.4f} | Current: ${perf['current_price']:.4f}
• {pnl_emoji} PnL: {perf['pnl_percentage']:+.1f}% (${perf['unrealized_pnl']:+,.2f})
• {daily_emoji} 24h: {perf['daily_change']:+.1f}% (${perf['daily_value_change']:+,.2f})"""

        # Add alerts if any
        alerts = portfolio_data.get('alerts', [])
        if alerts:
            message += "\n\n🔔 *Alerts:*"
            for alert in alerts[:3]:  # Show top 3 alerts
                message += f"\n• {alert}"

        return message

    def _get_explorer_link(self, blockchain: str, tx_hash: str) -> str:
        """Get blockchain explorer link for transaction"""
        if blockchain == 'ETH':
            return f"https://etherscan.io/tx/{tx_hash}"
        elif blockchain == 'BTC':
            return f"https://blockchain.info/tx/{tx_hash}"
        return ""
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_message = """
📊 Welcome to Your Portfolio Tracker!

Available commands:
/check - Get current portfolio performance
/portfolio - Detailed portfolio breakdown
/help - Show this help message

The bot automatically sends daily portfolio digests at 09:00 UTC.
Track your crypto journey! 🚀
        """
        await update.message.reply_text(welcome_message)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""

        # Get enabled coins info
        try:
            enabled_coins = self.api_handler.get_enabled_coins_info()
            coins_list = []
            for symbol, info in enabled_coins.items():
                coins_list.append(f"• {symbol} ({info['name']}) via {info['api_provider']}")

            coins_text = "\n".join(coins_list) if coins_list else "• No coins configured"

        except Exception as e:
            coins_text = "• Bitcoin and Ethereum (legacy mode)"

        help_message = f"""
🐋 Crypto Whale Tracker Commands:

/start - Welcome message and setup
/check - Get whale transactions from last 24 hours
/coins - Show tracked coins and status
/help - Show this help message

📊 Currently tracking:
{coins_text}

💰 Whale threshold: $500,000+ transactions
⏰ Daily digest sent at 09:00 UTC
🔗 Real-time price data from CoinGecko
        """
        await update.message.reply_text(help_message)
    
    async def check_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /check command - get current portfolio performance"""
        try:
            # Send "typing" indicator
            await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")

            # Send initial message
            status_message = await update.message.reply_text("📊 Checking portfolio performance...")

            # Get portfolio digest
            portfolio_digest = get_portfolio_digest()

            # Delete status message
            await status_message.delete()

            # Send portfolio digest
            if portfolio_digest and "Unable to generate" not in portfolio_digest:
                await update.message.reply_text(
                    portfolio_digest,
                    parse_mode='Markdown',
                    disable_web_page_preview=True
                )
            else:
                await update.message.reply_text(
                    "❌ Unable to fetch portfolio data. Please check your configuration."
                )

        except Exception as e:
            logger.error(f"Error in check command: {e}")
            await update.message.reply_text(
                "❌ Sorry, there was an error fetching portfolio data. Please try again later."
            )

    async def portfolio_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /portfolio command - show detailed portfolio breakdown"""
        try:
            # Send "typing" indicator
            await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")

            # Send initial message
            status_message = await update.message.reply_text("📊 Getting detailed portfolio data...")

            # Get detailed portfolio performance
            portfolio_data = get_portfolio_performance()

            # Delete status message
            await status_message.delete()

            if portfolio_data:
                # Format detailed breakdown
                message = self._format_detailed_portfolio(portfolio_data)
                await update.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    disable_web_page_preview=True
                )
            else:
                await update.message.reply_text(
                    "❌ Unable to fetch detailed portfolio data. Please check your configuration."
                )

        except Exception as e:
            logger.error(f"Error in portfolio command: {e}")
            await update.message.reply_text(
                "❌ Sorry, there was an error fetching detailed portfolio data. Please try again later."
            )

    async def coins_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /coins command - show tracked coins and status"""
        try:
            # Get enabled coins info
            enabled_coins = self.api_handler.get_enabled_coins_info()

            if not enabled_coins:
                await update.message.reply_text("No coins are currently configured for tracking.")
                return

            message = "🪙 **Tracked Coins Status**\n\n"

            for symbol, info in enabled_coins.items():
                status_emoji = "✅" if info.get('has_handler', False) else "❌"
                message += f"{status_emoji} **{symbol}** ({info['name']})\n"
                message += f"   Chain: {info['chain_type']}\n"
                message += f"   API: {info['api_provider']}\n\n"

            message += f"💰 Whale threshold: $500,000+\n"
            message += f"📊 Total coins tracked: {len(enabled_coins)}"

            await update.message.reply_text(message, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error in coins command: {e}")
            await update.message.reply_text(
                "❌ Error retrieving coin information. Please try again later."
            )
    
    async def send_portfolio_digest(self, chat_id: str, portfolio_digest: str = None):
        """Send portfolio digest to specified chat"""
        try:
            if not portfolio_digest:
                portfolio_digest = get_portfolio_digest()

            if portfolio_digest and "Unable to generate" not in portfolio_digest:
                await self.application.bot.send_message(
                    chat_id=chat_id,
                    text=portfolio_digest,
                    parse_mode='Markdown',
                    disable_web_page_preview=True
                )
                logger.info("Portfolio digest sent successfully")
            else:
                await self.application.bot.send_message(
                    chat_id=chat_id,
                    text="❌ Unable to generate portfolio digest. Please check your configuration."
                )
                logger.warning("Failed to generate portfolio digest")

        except Exception as e:
            logger.error(f"Error sending portfolio digest: {e}")

    async def send_daily_digest(self, chat_id: str):
        """Send daily whale digest to specified chat"""
        try:
            logger.info("Sending daily whale digest")
            
            # Get whale transactions from last 24 hours
            transactions = self.api_handler.get_all_whale_transactions(hours_back=24)
            
            # Format messages
            current_date = datetime.now().strftime("%Y-%m-%d")
            title = f"🐋 Daily Whale Digest - {current_date}"
            messages = self.format_whale_message(transactions, title)
            
            # Send messages
            for message in messages:
                await self.application.bot.send_message(
                    chat_id=chat_id,
                    text=message,
                    parse_mode='Markdown',
                    disable_web_page_preview=True
                )
            
            logger.info(f"Daily digest sent successfully with {len(transactions)} transactions")
            
        except Exception as e:
            logger.error(f"Error sending daily digest: {e}")
            # Send error notification
            try:
                await self.application.bot.send_message(
                    chat_id=chat_id,
                    text="❌ Error generating daily whale digest. Please check logs."
                )
            except Exception as send_error:
                logger.error(f"Failed to send error notification: {send_error}")
    
    def setup_bot(self):
        """Setup the Telegram bot with handlers"""
        # Create application
        self.application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()
        
        # Add command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("check", self.check_command))
        self.application.add_handler(CommandHandler("portfolio", self.portfolio_command))
        self.application.add_handler(CommandHandler("coins", self.coins_command))
        
        logger.info("Telegram bot setup complete")
        
        return self.application
    
    async def start_polling(self):
        """Start the bot in polling mode"""
        if not self.application:
            self.setup_bot()
        
        logger.info("Starting Telegram bot polling...")
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
        return self.application
    
    async def stop_polling(self):
        """Stop the bot polling"""
        if self.application and self.application.updater:
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()
            logger.info("Telegram bot stopped")

# Simple main function to run the bot
if __name__ == "__main__":
    import asyncio

    logging.basicConfig(level=logging.INFO)
    logger.info("🚀 Starting Portfolio Bot...")

    bot = PortfolioTrackerBot()
    app = bot.setup_bot()

    # Run the bot
    app.run_polling()

if __name__ == "__main__":
    """Run the bot when script is executed directly"""
    import asyncio

    # Setup logging
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )

    # Validate configuration
    try:
        config.validate_config()
        logger.info("Configuration validated successfully")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        exit(1)

    # Create and start bot
    bot = PortfolioTrackerBot()

    async def main():
        """Main async function to run the bot"""
        try:
            logger.info("🚀 Starting Portfolio Tracker Bot...")
            await bot.start_polling()

            # Keep the bot running
            logger.info("✅ Bot is running! Send /start to your bot to test it.")
            logger.info("Press Ctrl+C to stop the bot.")

            # Run until interrupted
            import signal
            stop_event = asyncio.Event()

            def signal_handler():
                logger.info("Received stop signal, shutting down...")
                stop_event.set()

            # Handle Ctrl+C gracefully
            loop = asyncio.get_running_loop()
            for sig in [signal.SIGTERM, signal.SIGINT]:
                loop.add_signal_handler(sig, signal_handler)

            await stop_event.wait()

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error(f"Error running bot: {e}")
        finally:
            await bot.stop_polling()
            logger.info("Bot stopped.")

    # Run the bot
    asyncio.run(main())
