#!/usr/bin/env python3
"""
Test script to verify the fixes we made:
1. ETH balance fetching with Etherscan V2 API
2. Price fetching for enabled coins only
3. Disabled coins are not included
"""

import logging
from api_handler import APIHandler
from portfolio_config import get_enabled_portfolio_coins

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_eth_balance_fetching():
    """Test ETH balance fetching with a known address"""
    print("🔍 Testing ETH balance fetching...")
    
    api_handler = APIHandler()
    
    # Test with a known Ethereum address (<PERSON><PERSON>'s address)
    test_address = "******************************************"
    
    try:
        balance = api_handler._get_eth_balance(test_address)
        if balance >= 0:
            print(f"✅ ETH balance fetching works! Address {test_address[:10]}... has {balance:.6f} ETH")
            return True
        else:
            print(f"❌ ETH balance fetching returned negative value: {balance}")
            return False
    except Exception as e:
        print(f"❌ ETH balance fetching failed: {e}")
        return False

def test_price_fetching():
    """Test price fetching for enabled coins"""
    print("\n🔍 Testing price fetching for enabled coins...")
    
    api_handler = APIHandler()
    enabled_coins = get_enabled_portfolio_coins()
    
    print(f"Enabled coins: {enabled_coins}")
    
    try:
        prices = api_handler.get_current_prices()
        
        print(f"\nFetched prices for {len(prices)} coins:")
        for coin, price in prices.items():
            status = "✅" if price > 0 else "❌"
            print(f"  {status} {coin}: ${price:.6f}")
        
        # Check if disabled coins are excluded
        disabled_coins = ['SHIB', 'FUN', 'VTHO', 'NFP']
        excluded_count = 0
        for coin in disabled_coins:
            if coin not in prices or prices.get(coin, 0) == 0:
                excluded_count += 1
                print(f"  ✅ {coin}: Correctly excluded (disabled)")
        
        # Check if major coins have prices
        major_coins = ['BTC', 'ETH', 'ADA', 'BNB']
        working_major = 0
        for coin in major_coins:
            if coin in prices and prices[coin] > 0:
                working_major += 1
        
        print(f"\nSummary:")
        print(f"  - Major coins with prices: {working_major}/{len(major_coins)}")
        print(f"  - Disabled coins excluded: {excluded_count}/{len(disabled_coins)}")
        
        return working_major >= 3  # At least 3 major coins should work
        
    except Exception as e:
        print(f"❌ Price fetching failed: {e}")
        return False

def test_portfolio_balances():
    """Test portfolio balance fetching"""
    print("\n🔍 Testing portfolio balance fetching...")
    
    api_handler = APIHandler()
    
    try:
        balances = api_handler.get_portfolio_balances()
        
        print(f"Portfolio balances found: {len(balances)} assets")
        for asset, balance in balances.items():
            if balance > 0:
                print(f"  ✅ {asset}: {balance:.6f}")
        
        return len(balances) > 0
        
    except Exception as e:
        print(f"❌ Portfolio balance fetching failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Whale Tracker Fixes")
    print("=" * 50)
    
    tests = [
        ("ETH Balance Fetching", test_eth_balance_fetching),
        ("Price Fetching", test_price_fetching),
        ("Portfolio Balances", test_portfolio_balances)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            if test_func():
                print(f"✅ PASS: {test_name}")
                passed += 1
            else:
                print(f"❌ FAIL: {test_name}")
        except Exception as e:
            print(f"❌ ERROR in {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes working correctly!")
    else:
        print("⚠️  Some issues remain - check the output above")

if __name__ == "__main__":
    main()
