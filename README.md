# 📊 Personal Crypto Portfolio Tracker

A Python-based personal cryptocurrency portfolio tracker that monitors your holdings across multiple wallets, calculates PnL vs cost basis, sends daily performance digests via Telegram, and integrates with your existing transaction history in Google Sheets.

## Features

- **Portfolio Performance Tracking**: Calculates PnL vs weighted average cost basis
- **Multi-Wallet Support**: Monitors Bitcoin, Ethereum, and other addresses
- **Smart Cost Basis**: Reads your existing transaction history from Google Sheets
- **Telegram Integration**: Daily digests and on-demand portfolio checks via `/check` and `/portfolio` commands
- **Performance Metrics**: Shows both daily changes AND lifetime performance vs your buy prices
- **Smart Alerts**: Notifications for significant moves and profit-taking opportunities
- **Real-time Pricing**: Fetches current crypto prices from CoinGecko
- **Modular Design**: Well-structured, maintainable codebase

## Tech Stack

- **Python 3.10+**
- **python-telegram-bot**: Telegram bot integration (polling mode)
- **requests**: HTTP API calls
- **gspread + google-auth**: Google Sheets integration
- **schedule**: Daily automation
- **python-dotenv**: Environment variable management

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <your-repo>
cd whale-watch
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Configure API Keys

Copy the example environment file and fill in your API keys:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

#### Telegram Bot Setup
1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Create a new bot with `/newbot`
3. Copy the bot token to `TELEGRAM_BOT_TOKEN`
4. Message your bot, then visit `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
5. Find your chat ID in the response and add to `TELEGRAM_CHAT_ID`

#### Etherscan API Key
1. Visit [Etherscan APIs](https://etherscan.io/apis)
2. Create a free account and generate an API key
3. Add to `ETHERSCAN_API_KEY`

#### Google Sheets Setup
1. Create a [Google Cloud Project](https://console.cloud.google.com/)
2. Enable the Google Sheets API
3. Create a Service Account and download `credentials.json`
4. Create a new Google Sheet and share it with the service account email
5. Copy the Sheet ID from the URL to `GOOGLE_SHEETS_ID`

### 3. Test the Setup

```bash
python main.py test
```

This will test all components and verify your configuration.

### 4. Run the Tracker

```bash
python main.py
```

The tracker will:
- Start the Telegram bot (polling mode)
- Schedule daily whale checks at 09:00 UTC
- Send a startup notification to your Telegram

## Usage

### Telegram Commands

- `/start` - Welcome message and setup info
- `/check` - Get whale transactions from the last 24 hours
- `/help` - Show available commands

### Daily Digest

The bot automatically sends a daily digest at 09:00 UTC containing:
- All whale transactions (>$500k) from the last 24 hours
- Transaction details with blockchain explorer links
- USD values at current prices

### Google Sheets Data

Transaction data is automatically saved to your Google Sheet with columns:
- Timestamp
- Blockchain (ETH/BTC)
- Transaction Hash
- From/To Addresses
- Amount & Currency
- USD Value
- Current Price
- Block Number

## Configuration

Key settings in `config.py`:

- `WHALE_THRESHOLD_USD`: Minimum transaction value ($500k default)
- `DAILY_CHECK_TIME`: Time for daily digest ("09:00" default)
- `LOOKBACK_HOURS`: How far back to check (24 hours default)
- Rate limiting settings for each API

## API Usage & Limits

The tracker is designed to work within free API tier limits:

- **Etherscan**: 5 calls/second, 100k calls/day (free tier)
- **CoinGecko**: Conservative rate limiting for free tier
- **Blockchain.info**: No official limits, but conservative approach
- **Telegram**: No limits for bot messaging

## File Structure

```
whale-watch/
├── main.py              # Main orchestrator
├── config.py            # Configuration and environment variables
├── api_handler.py       # Blockchain data fetching with rate limiting
├── telegram_bot.py      # Telegram bot commands and messaging
├── sheets_handler.py    # Google Sheets data storage
├── requirements.txt     # Python dependencies
├── .env.example         # Environment variables template
├── .env                 # Your actual environment variables (create this)
├── credentials.json     # Google Service Account credentials (download this)
└── README.md           # This file
```

## Deployment Options

### Local Development
Run directly with `python main.py` for testing and development.

### Production Deployment
- **VPS/Server**: Run with process manager like `systemd` or `supervisor`
- **Cloud Functions**: Adapt for serverless deployment (Vercel, AWS Lambda)
- **Docker**: Containerize for easy deployment

## Troubleshooting

### Common Issues

1. **"Missing required environment variables"**
   - Check your `.env` file has all required values
   - Ensure `.env` is in the same directory as `main.py`

2. **Google Sheets authentication errors**
   - Verify `credentials.json` is in the project directory
   - Ensure the service account email has access to your sheet
   - Check the Google Sheets ID is correct

3. **Telegram bot not responding**
   - Verify bot token is correct
   - Check chat ID is your actual chat ID with the bot
   - Ensure bot is not blocked

4. **API rate limit errors**
   - The tracker includes built-in rate limiting
   - Check API key validity
   - Monitor logs for specific error messages

### Logs

Check `whale_tracker.log` for detailed operation logs and error messages.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Disclaimer

This tool is for educational and informational purposes only. Cryptocurrency markets are volatile and unpredictable. Always do your own research before making any investment decisions.
