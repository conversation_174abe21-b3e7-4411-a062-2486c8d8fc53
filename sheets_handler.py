"""
📊 GOOGLE SHEETS HANDLER - sheets_handler.py

This is your DATA STORAGE SYSTEM! This file:

1. 💾 Saves all whale transactions to Google Sheets
2. 📈 Creates organized spreadsheets for analysis
3. 🔍 Prevents duplicate transactions
4. 📋 Formats data with timestamps, amounts, USD values
5. 🧹 Can clean up old data automatically

WHAT IT DOES:
- Connects to your Google Sheet using service account
- Automatically saves every whale transaction found
- Organizes data in columns (Date, Coin, Amount, USD Value, etc.)
- Lets you analyze whale patterns over time

HOW TO USE:
- You don't run this directly
- Set up Google Sheets credentials (credentials.json)
- The system automatically saves whale data here

This is your "whale transaction database" for long-term analysis!
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional
import gspread
from google.oauth2.service_account import Credentials
import config

logger = logging.getLogger(__name__)

class SheetsHandler:
    def __init__(self):
        self.client = None
        self.sheet = None
        self.worksheet = None
        self._initialize_sheets()
    
    def _initialize_sheets(self):
        """Initialize Google Sheets connection"""
        try:
            # Define the scope
            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]
            
            # Load credentials
            credentials = Credentials.from_service_account_file(
                config.GOOGLE_SHEETS_CREDENTIALS_FILE,
                scopes=scope
            )
            
            # Create client
            self.client = gspread.authorize(credentials)
            
            # Open the spreadsheet
            self.sheet = self.client.open_by_key(config.GOOGLE_SHEETS_ID)
            
            # Get or create the worksheet
            self.worksheet = self._get_or_create_worksheet()

            # Update headers if needed (for existing worksheets)
            if self.worksheet:
                self._update_headers_if_needed()

            logger.info("Google Sheets connection initialized successfully")
            
        except FileNotFoundError:
            logger.error(f"Credentials file not found: {config.GOOGLE_SHEETS_CREDENTIALS_FILE}")
            logger.error("Please download your Google Service Account credentials and save as credentials.json")
        except Exception as e:
            logger.error(f"Failed to initialize Google Sheets: {e}")
    
    def _get_or_create_worksheet(self):
        """Get existing worksheet or create new one with headers"""
        try:
            # Try to get existing worksheet
            worksheet = self.sheet.worksheet(config.SHEET_NAME)
            logger.info(f"Found existing worksheet: {config.SHEET_NAME}")
            return worksheet
            
        except gspread.WorksheetNotFound:
            # Create new worksheet
            logger.info(f"Creating new worksheet: {config.SHEET_NAME}")
            worksheet = self.sheet.add_worksheet(
                title=config.SHEET_NAME,
                rows=1000,
                cols=len(config.SHEET_HEADERS)
            )
            
            # Add headers
            worksheet.append_row(config.SHEET_HEADERS)
            
            # Format headers (bold) - Updated range for new columns
            worksheet.format('A1:S1', {
                'textFormat': {'bold': True},
                'backgroundColor': {'red': 0.9, 'green': 0.9, 'blue': 0.9}
            })
            
            logger.info("Worksheet created with headers")
            return worksheet

    def _update_headers_if_needed(self):
        """Update worksheet headers if they don't match current config"""
        if not self.worksheet:
            return

        try:
            # Get current headers
            current_headers = self.worksheet.row_values(1)
            expected_headers = config.SHEET_HEADERS

            # Check if headers need updating
            if len(current_headers) != len(expected_headers) or current_headers != expected_headers:
                logger.info(f"Updating headers from {len(current_headers)} to {len(expected_headers)} columns")

                # Clear the first row and add new headers
                self.worksheet.delete_rows(1)
                self.worksheet.insert_row(expected_headers, 1)

                # Format headers (bold)
                header_range = f"A1:{chr(65 + len(expected_headers) - 1)}1"  # A1:S1 for 19 columns
                self.worksheet.format(header_range, {
                    'textFormat': {'bold': True},
                    'backgroundColor': {'red': 0.9, 'green': 0.9, 'blue': 0.9}
                })

                logger.info("Headers updated successfully")
            else:
                logger.info("Headers are already up to date")

        except Exception as e:
            logger.error(f"Error updating headers: {e}")

    def save_transactions(self, transactions: List[Dict]) -> bool:
        """Save whale transactions to Google Sheets"""
        if not self.worksheet:
            logger.error("Google Sheets not initialized")
            return False
        
        if not transactions:
            logger.info("No transactions to save")
            return True
        
        try:
            # Prepare data rows
            rows_to_add = []
            
            for tx in transactions:
                row = self._format_transaction_row(tx)
                rows_to_add.append(row)
            
            # Check for duplicates before adding
            existing_hashes = self._get_existing_transaction_hashes()
            new_rows = [row for row in rows_to_add if row[2] not in existing_hashes]  # Hash is in column 2
            
            if not new_rows:
                logger.info("All transactions already exist in sheet")
                return True
            
            # Add new rows
            self.worksheet.append_rows(new_rows)
            
            logger.info(f"Added {len(new_rows)} new transactions to Google Sheets")
            return True
            
        except Exception as e:
            logger.error(f"Error saving transactions to Google Sheets: {e}")
            return False
    
    def _format_transaction_row(self, tx: Dict) -> List[str]:
        """Format a transaction dictionary into a spreadsheet row"""
        timestamp = tx.get('timestamp', datetime.now())
        if isinstance(timestamp, datetime):
            timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S UTC")
        else:
            timestamp_str = str(timestamp)

        return [
            timestamp_str,                              # Timestamp
            tx.get('blockchain', ''),                   # Blockchain
            tx.get('hash', ''),                         # Transaction Hash
            tx.get('from', ''),                         # From Address
            tx.get('to', ''),                           # To Address
            str(tx.get('amount', 0)),                   # Amount
            tx.get('currency', ''),                     # Currency
            str(tx.get('usd_value', 0)),               # USD Value
            str(tx.get('current_price', 0)),           # Current Price
            str(tx.get('block_number', '')),           # Block Number
            # Enhanced learning data columns
            str(tx.get('price_1h_before', '')),        # Price_Before_1h
            str(tx.get('price_1h_after', '')),         # Price_After_1h
            str(tx.get('price_6h_after', '')),         # Price_After_6h
            tx.get('from_exchange', ''),               # From_Exchange
            tx.get('to_exchange', ''),                 # To_Exchange
            tx.get('transaction_type', ''),            # Transaction_Type
            str(tx.get('alert_sent', '')),             # Alert_Sent
            str(tx.get('round_amount', '')),           # Round_Amount
            str(tx.get('user_rating', ''))             # User_Rating
        ]
    
    def _get_existing_transaction_hashes(self) -> set:
        """Get set of existing transaction hashes to avoid duplicates"""
        try:
            # Get all values from the hash column (column C, index 2)
            hash_column = self.worksheet.col_values(3)  # 1-indexed, so column C is 3
            
            # Remove header and empty values
            existing_hashes = set(hash_column[1:]) if len(hash_column) > 1 else set()
            existing_hashes.discard('')  # Remove empty strings
            
            return existing_hashes
            
        except Exception as e:
            logger.error(f"Error getting existing transaction hashes: {e}")
            return set()
    
    def get_transaction_summary(self, days: int = 7) -> Optional[Dict]:
        """Get summary statistics from the last N days"""
        if not self.worksheet:
            return None
        
        try:
            # Get all data
            all_data = self.worksheet.get_all_records()
            
            if not all_data:
                return {
                    'total_transactions': 0,
                    'total_usd_value': 0,
                    'eth_transactions': 0,
                    'btc_transactions': 0,
                    'largest_transaction': None
                }
            
            # Filter by date range
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
            
            recent_transactions = []
            for row in all_data:
                try:
                    timestamp_str = row.get('Timestamp', '')
                    if timestamp_str:
                        tx_date = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S UTC")
                        if tx_date >= cutoff_date:
                            recent_transactions.append(row)
                except ValueError:
                    continue
            
            # Calculate summary
            total_transactions = len(recent_transactions)
            total_usd_value = sum(float(tx.get('USD Value', 0)) for tx in recent_transactions)
            eth_transactions = len([tx for tx in recent_transactions if tx.get('Currency') == 'ETH'])
            btc_transactions = len([tx for tx in recent_transactions if tx.get('Currency') == 'BTC'])
            
            # Find largest transaction
            largest_transaction = None
            if recent_transactions:
                largest_transaction = max(
                    recent_transactions,
                    key=lambda x: float(x.get('USD Value', 0))
                )
            
            return {
                'total_transactions': total_transactions,
                'total_usd_value': total_usd_value,
                'eth_transactions': eth_transactions,
                'btc_transactions': btc_transactions,
                'largest_transaction': largest_transaction,
                'days_analyzed': days
            }
            
        except Exception as e:
            logger.error(f"Error getting transaction summary: {e}")
            return None
    
    def backup_data(self) -> Optional[List[Dict]]:
        """Get all transaction data as backup"""
        if not self.worksheet:
            return None
        
        try:
            all_data = self.worksheet.get_all_records()
            logger.info(f"Backed up {len(all_data)} transactions")
            return all_data
            
        except Exception as e:
            logger.error(f"Error backing up data: {e}")
            return None
    
    def clear_old_data(self, days_to_keep: int = 90):
        """Clear transaction data older than specified days"""
        if not self.worksheet:
            logger.error("Google Sheets not initialized")
            return False
        
        try:
            # Get all data with row numbers
            all_data = self.worksheet.get_all_values()
            
            if len(all_data) <= 1:  # Only headers or empty
                return True
            
            # Calculate cutoff date
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days_to_keep)
            
            # Find rows to delete (working backwards to maintain row indices)
            rows_to_delete = []
            for i, row in enumerate(all_data[1:], start=2):  # Skip header, start from row 2
                try:
                    timestamp_str = row[0] if row else ''
                    if timestamp_str:
                        tx_date = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S UTC")
                        if tx_date < cutoff_date:
                            rows_to_delete.append(i)
                except (ValueError, IndexError):
                    continue
            
            # Delete rows (from bottom to top to maintain indices)
            for row_num in reversed(rows_to_delete):
                self.worksheet.delete_rows(row_num)
            
            logger.info(f"Deleted {len(rows_to_delete)} old transactions (older than {days_to_keep} days)")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing old data: {e}")
            return False
