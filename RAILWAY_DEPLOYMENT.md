# 🚀 Railway Deployment Guide

Deploy your Portfolio Tracker to Railway for 24/7 operation!

## 📋 Prerequisites

✅ **Working locally** - Your bot should work on your computer first  
✅ **All API keys** - Telegram, Etherscan, Binance, etc.  
✅ **Google Sheets** - Working with your credentials  
✅ **Base64 credentials** - Your Google credentials encoded in base64  

## 🔧 Step 1: Prepare Your Credentials

### Convert Google Credentials to Base64

```bash
# On Windows (PowerShell)
$fileContent = Get-Content "credentials.json" -Raw
$bytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
$base64 = [System.Convert]::ToBase64String($bytes)
Write-Output $base64

# On Mac/Linux
base64 -i credentials.json
```

Copy the output - this is your `GOOGLE_CREDENTIALS_BASE64` value.

## 🚀 Step 2: Deploy to Railway

### Option A: Deploy from GitHub (Recommended)

1. **Push to GitHub** (if not already):
   ```bash
   git init
   git add .
   git commit -m "Portfolio tracker ready for deployment"
   git branch -M main
   git remote add origin https://github.com/yourusername/portfolio-tracker.git
   git push -u origin main
   ```

2. **Deploy on Railway**:
   - Go to [railway.app](https://railway.app)
   - Click "Start a New Project"
   - Choose "Deploy from GitHub repo"
   - Select your repository
   - Railway will auto-detect Python and deploy

### Option B: Deploy with Railway CLI

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

## ⚙️ Step 3: Configure Environment Variables

In Railway dashboard, go to your project → Variables tab and add:

### 🤖 Required Variables
```
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
ETHERSCAN_API_KEY=your_etherscan_api_key_here
GOOGLE_SHEETS_ID=your_google_sheets_id_here
GOOGLE_CREDENTIALS_BASE64=your_base64_encoded_credentials_here
```

### 💰 Exchange APIs (Optional)
```
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_secret_here
```

### 🔑 Additional APIs (Optional)
```
BLOCKFROST_API_KEY=your_blockfrost_api_key_here
WHALE_THRESHOLD_USD=500000
```

## 🔍 Step 4: Verify Deployment

1. **Check Logs**: In Railway dashboard → Deployments → View Logs
2. **Test Bot**: Send `/start` to your Telegram bot
3. **Test Portfolio**: Send `/portfolio` command

Expected log output:
```
Configuration validated successfully
Telegram bot started successfully
Portfolio Tracker started successfully
```

## 🆓 Railway Free Tier

**What you get FREE:**
- ✅ $5/month credit (enough for small bots)
- ✅ 512MB RAM
- ✅ Always-on deployment
- ✅ Custom domains
- ✅ Automatic deployments from GitHub

**Perfect for your portfolio tracker!** 🎯

## 🔧 Troubleshooting

### Common Issues:

**❌ "Missing required environment variables"**
- Check all required variables are set in Railway dashboard
- Verify variable names match exactly (case-sensitive)

**❌ "Google Sheets connection failed"**
- Verify `GOOGLE_CREDENTIALS_BASE64` is correctly encoded
- Check `GOOGLE_SHEETS_ID` is correct
- Ensure Google Sheets API is enabled

**❌ "Telegram bot not responding"**
- Verify `TELEGRAM_BOT_TOKEN` is correct
- Check `TELEGRAM_CHAT_ID` matches your chat
- Look for errors in Railway logs

**❌ "ETH balance showing 0"**
- Verify `ETHERSCAN_API_KEY` is valid
- Check API key has sufficient quota
- Ensure addresses in `portfolio_config.py` are correct

### Debug Commands:
```bash
# View logs
railway logs

# Connect to shell
railway shell

# Check environment variables
railway variables
```

## 🎉 Success!

Your portfolio tracker is now running 24/7 on Railway! 

**What happens now:**
- ✅ Bot responds to Telegram commands instantly
- ✅ Daily portfolio updates at 9 AM UTC
- ✅ Real-time balance tracking
- ✅ Automatic price updates
- ✅ Google Sheets logging

**Commands to try:**
- `/start` - Welcome message
- `/portfolio` - Full portfolio breakdown
- `/check` - Quick performance summary
- `/help` - All available commands

## 🔄 Updates

To update your deployed bot:
1. Make changes locally
2. Push to GitHub: `git push`
3. Railway auto-deploys from GitHub

## 🆘 Support

If you need help:
1. Check Railway logs for errors
2. Verify all environment variables
3. Test locally first
4. Check API quotas and limits

Your portfolio tracker is now live! 🚀📊
