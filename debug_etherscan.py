#!/usr/bin/env python3
"""
Debug script to test Etherscan API directly and see what's happening
"""

import requests
import json
import config

def test_etherscan_api_direct():
    """Test Etherscan API directly with detailed logging"""
    print("🔍 Testing Etherscan API directly...")
    print(f"API Key: {config.ETHERSCAN_API_KEY}")
    print(f"Base URL: {config.ETHERSCAN_BASE_URL}")
    
    # Test 1: Get latest block number (simple test)
    print("\n📋 Test 1: Get latest block number")
    url = config.ETHERSCAN_BASE_URL
    params = {
        'chainid': 1,  # Ethereum mainnet
        'module': 'proxy',
        'action': 'eth_blockNumber',
        'apikey': config.ETHERSCAN_API_KEY
    }
    
    print(f"Request URL: {url}")
    print(f"Request params: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Latest block: {data}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    # Test 2: Get balance for a known address
    print("\n📋 Test 2: Get ETH balance for Vitalik's address")
    test_address = "******************************************"  # Vitalik's address
    
    params = {
        'chainid': 1,  # Ethereum mainnet
        'module': 'account',
        'action': 'balance',
        'address': test_address,
        'tag': 'latest',
        'apikey': config.ETHERSCAN_API_KEY
    }
    
    print(f"Request URL: {url}")
    print(f"Request params: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Raw response: {data}")
            
            if data.get('status') == '1':
                wei = int(data['result'])
                eth_balance = wei / 1e18
                print(f"✅ ETH balance: {eth_balance:.6f} ETH")
            else:
                print(f"❌ API Error: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    # Test 3: Check API key usage
    print("\n📋 Test 3: Check API key status")
    params = {
        'chainid': 1,  # Ethereum mainnet
        'module': 'stats',
        'action': 'ethsupply',
        'apikey': config.ETHERSCAN_API_KEY
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == '1':
                print(f"✅ API key is working - ETH supply: {data['result']}")
            else:
                print(f"❌ API Error: {data.get('message', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_your_eth_address():
    """Test with your actual ETH address from portfolio_config"""
    print("\n📋 Test 4: Check your actual ETH address")
    
    from portfolio_config import PORTFOLIO_ADDRESSES
    
    eth_addresses = [addr for addr in PORTFOLIO_ADDRESSES if addr.chain == "ETH"]
    
    if not eth_addresses:
        print("❌ No ETH addresses found in portfolio_config.py")
        return
    
    for addr_obj in eth_addresses:
        address = addr_obj.address
        print(f"\nTesting address: {address} ({addr_obj.label})")
        
        url = config.ETHERSCAN_BASE_URL
        params = {
            'chainid': 1,  # Ethereum mainnet
            'module': 'account',
            'action': 'balance',
            'address': address,
            'tag': 'latest',
            'apikey': config.ETHERSCAN_API_KEY
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            print(f"Response status: {response.status_code}")
            print(f"Response text: {response.text}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == '1':
                    wei = int(data['result'])
                    eth_balance = wei / 1e18
                    print(f"✅ {addr_obj.label}: {eth_balance:.6f} ETH")
                else:
                    print(f"❌ API Error: {data.get('message', 'Unknown error')}")
            
        except Exception as e:
            print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_etherscan_api_direct()
    test_your_eth_address()
