# 🔒 SECURITY GUIDE - PROTECT YOUR API KEYS!

## 🚨 CRITICAL: What Just Happened?

Your credential files were exposed in the project directory. **This has been FIXED** but here's what you need to know:

### **Files Removed (GOOD!):**
- ✅ `credentials.json` - Google Service Account key
- ✅ `whalewatcher-473617-95db25c18000.json` - Another credential file

### **Current Status:**
- ✅ **SECURE**: Credential files removed from project
- ✅ **SECURE**: .gitignore properly configured
- ⚠️ **CAUTION**: .env file contains real keys (but not in git)

## 🛡️ SECURITY BEST PRACTICES

### **1. Environment Variables (.env file)**
```bash
# ✅ GOOD: Keep .env file LOCAL only
# ❌ NEVER commit .env to version control
# ❌ NEVER share .env file with anyone
```

### **2. Google Service Account Credentials**
```bash
# ✅ GOOD: Keep credentials.json LOCAL only  
# ❌ NEVER commit credentials.json to git
# ❌ NEVER share credentials files
```

### **3. API Key Security**
- **Telegram Bot Token**: Can control your bot
- **Etherscan API Key**: Can make requests on your behalf
- **Google Sheets ID**: Can access your spreadsheet
- **Blockfrost API Key**: Can access Cardano data

## 🔄 HOW TO REGENERATE COMPROMISED KEYS

If you think your keys were exposed:

### **Telegram Bot Token:**
1. Message @BotFather on Telegram
2. Send `/mybots`
3. Select your bot
4. Choose "API Token"
5. Select "Revoke current token"
6. Generate new token
7. Update your `.env` file

### **Etherscan API Key:**
1. Go to https://etherscan.io/myapikey
2. Delete old key
3. Create new key
4. Update your `.env` file

### **Google Service Account:**
1. Go to Google Cloud Console
2. IAM & Admin → Service Accounts
3. Delete old service account
4. Create new service account
5. Download new credentials.json
6. Update your `.env` file

### **Blockfrost API Key:**
1. Go to https://blockfrost.io/dashboard
2. Delete old project
3. Create new project
4. Copy new API key
5. Update your `.env` file

## 📋 DEPLOYMENT SECURITY CHECKLIST

**Before Deploying:**
- [ ] No credential files in project directory
- [ ] .env file is gitignored
- [ ] All secrets use environment variables
- [ ] No hardcoded API keys in code
- [ ] .gitignore includes all sensitive patterns

**For Cloud Deployment:**
- [ ] Use platform environment variables (not .env)
- [ ] Never upload credential files to cloud
- [ ] Use secrets management (Vercel env, Railway vars, etc.)
- [ ] Enable 2FA on all accounts

## 🚀 SAFE DEPLOYMENT PROCESS

### **Step 1: Prepare Locally**
```bash
# Ensure no sensitive files
ls -la | grep -E "\.(json|env)$"

# Should only see:
# .env.example (template - safe to commit)
# .env (your real keys - NEVER commit)
```

### **Step 2: Initialize Git (Optional)**
```bash
git init
git add .gitignore
git add .env.example
git add *.py *.md requirements.txt
# NEVER: git add .env
# NEVER: git add credentials.json
```

### **Step 3: Deploy to Platform**
```bash
# Use platform-specific environment variables
# Vercel: vercel env add KEY_NAME
# Railway: railway variables set KEY_NAME=value
# Heroku: heroku config:set KEY_NAME=value
```

## ⚠️ WARNING SIGNS

**🚨 IMMEDIATE ACTION NEEDED if you see:**
- Credential files in git repository
- API keys in code files
- .env file committed to git
- Sharing screenshots with API keys visible

**🔍 HOW TO CHECK:**
```bash
# Check what's in git (if using git)
git status
git log --oneline

# Look for sensitive files
find . -name "*.json" -o -name ".env"

# Check for hardcoded keys in code
grep -r "sk-" . || grep -r "bot.*:" . || grep -r "API.*=" .
```

## 🆘 IF KEYS ARE COMPROMISED

**IMMEDIATE ACTIONS:**
1. **Revoke all API keys** (see regeneration guide above)
2. **Change all passwords** on related accounts
3. **Enable 2FA** on all accounts
4. **Monitor accounts** for unusual activity
5. **Generate new keys** and update .env

**DON'T PANIC** - Most API keys have limited scope and can be easily regenerated!

## ✅ YOU'RE NOW SECURE!

Your whale tracker is now properly secured:
- ✅ No exposed credential files
- ✅ Proper .gitignore configuration  
- ✅ Environment variables properly handled
- ✅ Security documentation in place

**Ready for safe deployment!** 🛡️
