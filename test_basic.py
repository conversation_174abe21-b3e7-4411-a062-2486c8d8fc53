"""
Basic test script for Crypto Whale Tracker
Tests core functionality without requiring API keys
"""

import sys
import logging
from datetime import datetime
from api_handler import APIHandler
from telegram_bot import WhaleTrackerBot

# Set up basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

def test_api_handler():
    """Test API handler basic functionality"""
    logger.info("Testing API Handler...")
    
    api_handler = APIHandler()
    
    # Test price fetching (should work without API key)
    try:
        prices = api_handler.get_current_prices()
        logger.info(f"Current prices - ETH: ${prices['ETH']:.2f}, BTC: ${prices['BTC']:.2f}")
        
        if prices['ETH'] > 0 and prices['BTC'] > 0:
            logger.info("Price fetching test: PASSED")
        else:
            logger.warning("Price fetching test: FAILED - Got zero prices")
            
    except Exception as e:
        logger.error(f"Price fetching test: FAILED - {e}")
    
    # Test threshold calculation
    try:
        thresholds = api_handler.calculate_dynamic_thresholds(prices)
        logger.info(f"Dynamic thresholds - ETH: {thresholds['ETH']:.2f}, BTC: {thresholds['BTC']:.4f}")
        logger.info("Threshold calculation test: PASSED")
    except Exception as e:
        logger.error(f"Threshold calculation test: FAILED - {e}")

def test_telegram_bot():
    """Test Telegram bot basic functionality"""
    logger.info("Testing Telegram Bot...")
    
    try:
        bot = WhaleTrackerBot()
        
        # Test message formatting with sample data
        sample_transactions = [
            {
                'blockchain': 'ETH',
                'hash': '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
                'from': '******************************************',
                'to': '0xefgh5678901234567890abcdef1234567890abcd',
                'amount': 1500.0,
                'currency': 'ETH',
                'usd_value': 4500000,
                'current_price': 3000,
                'timestamp': datetime.now(),
                'block_number': '18500000'
            },
            {
                'blockchain': 'BTC',
                'hash': 'abcd1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd',
                'from': 'Multiple',
                'to': 'Multiple',
                'amount': 15.5,
                'currency': 'BTC',
                'usd_value': 930000,
                'current_price': 60000,
                'timestamp': datetime.now(),
                'block_number': '820000'
            }
        ]
        
        messages = bot.format_whale_message(sample_transactions, "Test Whale Alert")
        
        logger.info("Sample formatted message:")
        for i, message in enumerate(messages):
            logger.info(f"Message {i+1}:")
            print(message)
            print("-" * 50)
        
        logger.info("Message formatting test: PASSED")
        
    except Exception as e:
        logger.error(f"Telegram bot test: FAILED - {e}")

def test_configuration():
    """Test configuration loading"""
    logger.info("Testing Configuration...")
    
    try:
        import config
        
        # Test basic config values
        logger.info(f"Whale threshold: ${config.WHALE_THRESHOLD_USD:,}")
        logger.info(f"Daily check time: {config.DAILY_CHECK_TIME}")
        logger.info(f"Lookback hours: {config.LOOKBACK_HOURS}")
        logger.info(f"Rate limits - Etherscan: {config.ETHERSCAN_RATE_LIMIT}s, CoinGecko: {config.COINGECKO_RATE_LIMIT}s")
        
        logger.info("Configuration loading test: PASSED")
        
    except Exception as e:
        logger.error(f"Configuration test: FAILED - {e}")

def main():
    """Run basic tests"""
    logger.info("Starting basic Crypto Whale Tracker tests...")
    logger.info("=" * 60)
    
    # Test configuration
    test_configuration()
    print()
    
    # Test API handler
    test_api_handler()
    print()
    
    # Test Telegram bot
    test_telegram_bot()
    print()
    
    logger.info("=" * 60)
    logger.info("Basic tests completed!")
    logger.info("")
    logger.info("Next steps:")
    logger.info("1. Copy .env.example to .env and fill in your API keys")
    logger.info("2. Set up Google Sheets credentials (credentials.json)")
    logger.info("3. Run 'python main.py test' to test with real APIs")
    logger.info("4. Run 'python main.py' to start the whale tracker")

if __name__ == "__main__":
    main()
