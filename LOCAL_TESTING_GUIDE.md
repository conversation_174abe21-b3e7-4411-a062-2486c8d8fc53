# 🧪 Local Testing Guide - Get Real Whale Data!

Ready to see your whale tracker in action with real data? Let's set up your API keys and test everything locally.

## 🎯 **What You'll Accomplish**

By the end of this guide:
- ✅ Your whale tracker will fetch REAL whale transactions
- ✅ You'll receive actual Telegram messages with whale alerts
- ✅ You'll see current crypto prices and whale thresholds
- ✅ Everything will be tested and working perfectly

## 🔑 **Step 1: Get Your API Keys**

### **Telegram Bot Setup (Required)**

**1. Create Your Bot:**
- Open Telegram and message [@BotFather](https://t.me/BotFather)
- Type `/newbot`
- Choose a name: "My Whale Tracker"
- Choose a username: "yourname_whale_bot" (must end in 'bot')
- **Copy the bot token** (looks like: `*********:ABCdefGHIjklMNOpqrsTUVwxyz`)

**2. Get Your Chat ID:**
- Message your new bot (send any message)
- Visit this URL in your browser: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
- Replace `<YOUR_BOT_TOKEN>` with your actual token
- Look for `"chat":{"id":*********` - that number is your chat ID

### **Etherscan API Key (Required for ETH/WLD)**

**1. Create Account:**
- Go to https://etherscan.io/apis
- Click "Register" and create a free account
- Verify your email

**2. Generate API Key:**
- Login and go to "API Keys" section
- Click "Add" to create a new API key
- **Copy the API key** (looks like: `ABC123DEF456GHI789JKL`)

### **CoinGecko API Key (Optional)**

- Go to https://www.coingecko.com/en/api
- Free tier works without a key
- For higher limits, sign up and get a key

## 📝 **Step 2: Create Your .env File**

**1. Copy the example file:**
```bash
cp .env.example .env
```

**2. Edit the .env file with your actual keys:**
```bash
# Open .env in any text editor and fill in:

TELEGRAM_BOT_TOKEN=*********:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=*********
ETHERSCAN_API_KEY=ABC123DEF456GHI789JKL
COINGECKO_API_KEY=your_coingecko_key_optional

# Google Sheets (optional for now)
GOOGLE_SHEETS_ID=your_google_sheets_id_optional
GOOGLE_SHEETS_CREDENTIALS_FILE=credentials.json

# Whale threshold (optional - defaults to $500k)
WHALE_THRESHOLD_USD=500000
```

## 🧪 **Step 3: Test Everything**

### **Test 1: Basic Functionality**
```bash
python test_basic.py
```
**Expected Result:** All tests pass, shows current crypto prices

### **Test 2: Multi-Coin System**
```bash
python test_multi_coin.py
```
**Expected Result:** Shows 3 enabled coins (BTC, ETH, WLD) with real prices

### **Test 3: Coin Status**
```bash
python coin_manager.py
```
**Expected Result:** Shows enabled coins and validates your API keys

### **Test 4: Full System Test**
```bash
python main.py test
```
**Expected Result:** Tests all components with real APIs

## 🚀 **Step 4: Run Your Whale Tracker**

**Start the whale tracker:**
```bash
python main.py
```

**What happens:**
1. ✅ Telegram bot starts and sends you a startup message
2. ✅ System schedules daily whale checks for 9 AM UTC
3. ✅ Bot listens for your commands 24/7

## 📱 **Step 5: Test Telegram Commands**

**Open Telegram and message your bot:**

**1. Start command:**
```
/start
```
**Expected:** Welcome message with instructions

**2. Check for whales:**
```
/check
```
**Expected:** Real whale transactions from last 24 hours (or "no whales found")

**3. Show tracked coins:**
```
/coins
```
**Expected:** List of BTC, ETH, WLD with their status

**4. Help command:**
```
/help
```
**Expected:** List of all available commands

## 🐋 **What You'll See**

### **Sample Whale Alert Message:**
```
🐋 Whale Transactions (Last 24 Hours)

#1 ETH Transaction
💰 1,250.00 ETH ($5,125,000)
⏰ 14:30 UTC
🔗 View on Explorer

#2 BTC Transaction  
💰 85.5000 BTC ($9,576,000)
⏰ 12:15 UTC
🔗 View on Explorer

📊 Showing 2 whale transactions
```

### **Daily Digest (9 AM UTC):**
```
🐋 Daily Whale Digest - 2025-09-29

#1 BTC Transaction
💰 120.0000 BTC ($13,448,640)
⏰ 08:45 UTC
🔗 View on Explorer

No whale transactions found for ETH, WLD in the last 24 hours.
```

## 🔧 **Troubleshooting**

### **Common Issues:**

**1. "Missing required environment variables"**
```bash
# Check your .env file exists and has the right keys
cat .env

# Make sure you're in the right directory
pwd
# Should show: /c/Users/<USER>/Documents/Projects/Whale-watch
```

**2. "Telegram bot not responding"**
```bash
# Test your bot token manually
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getMe

# Should return bot information
```

**3. "API rate limit exceeded"**
```bash
# The system handles this automatically
# If you see this, wait a few minutes and try again
```

**4. "No whale transactions found"**
```bash
# This is normal! Whales don't move every day
# Try lowering the threshold temporarily for testing:
# In .env file: WHALE_THRESHOLD_USD=100000
```

### **Debug Commands:**

**Check logs:**
```bash
# Logs are saved to whale_tracker.log
tail -f whale_tracker.log
```

**Test individual components:**
```bash
# Test just price fetching
python -c "from api_handler import APIHandler; print(APIHandler().get_current_prices())"

# Test coin configuration
python -c "from coin_manager import CoinManager; print(CoinManager().validate_setup())"
```

## 🎉 **Success Indicators**

**You know it's working when:**
- ✅ `python main.py test` passes all tests
- ✅ Your Telegram bot responds to `/start`
- ✅ `/check` command returns whale data (or "no whales found")
- ✅ You receive a startup notification in Telegram
- ✅ No errors in the console or log file

## 🎯 **Next Steps After Local Testing**

**Once everything works locally:**

1. **Let it run**: Keep `python main.py` running to get daily whale alerts
2. **Add more coins**: Use `coin_manager.py` to enable additional cryptocurrencies
3. **Set up Google Sheets**: For long-term whale data analysis
4. **Deploy to cloud**: Use Vercel/Railway for 24/7 operation

## 💡 **Pro Tips**

**1. Test with lower threshold:**
```bash
# In .env file, temporarily set:
WHALE_THRESHOLD_USD=50000
# This will find more transactions for testing
```

**2. Check specific time periods:**
```bash
# The /check command looks at last 24 hours
# Whales are more active during market volatility
```

**3. Monitor multiple coins:**
```bash
# Enable more coins for better whale coverage
python -c "from coin_manager import CoinManager; CoinManager().enable_coins(['ASTR', 'ONE'])"
```

**Ready to test with real data?** Start with:
```bash
python main.py test
```

🚀 **Let's catch some whales!**
