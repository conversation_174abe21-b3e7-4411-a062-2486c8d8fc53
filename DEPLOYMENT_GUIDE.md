# 🚀 DEPLOYMENT GUIDE - Get Your Whale Tracker Online!

## 🤔 Platform Comparison

### **Vercel (NOT Recommended for this project)**
- ✅ Great for: Websites, APIs, scheduled functions
- ❌ Bad for: 24/7 background bots like yours
- ❌ Function timeout: 10 seconds (free), 5 minutes (paid)
- ❌ Not designed for persistent connections

### **Railway (RECOMMENDED)**
- ✅ Perfect for: 24/7 background applications
- ✅ Free tier: $5/month credit (usually enough)
- ✅ Easy Python deployment
- ✅ Built-in environment variables
- ✅ Automatic restarts if app crashes

### **Render (Good Alternative)**
- ✅ Free tier for background services
- ✅ Good for 24/7 apps
- ✅ Easy deployment from GitHub

## 🚀 RECOMMENDED: Railway Deployment

### **Step 1: Prepare Your Project**

**1.1 Restore Google Credentials:**
```bash
# Download your Google Service Account JSON from Google Cloud Console
# Rename it to: credentials.json
# Place it in your whale-watch directory
# (It's already gitignored, so it's secure)
```

**1.2 Verify Your .env File:**
```bash
# Make sure your .env contains all required keys:
TELEGRAM_BOT_TOKEN=your_actual_token
TELEGRAM_CHAT_ID=your_actual_chat_id
ETHERSCAN_API_KEY=your_actual_key
BLOCKFROST_API_KEY=your_actual_key
GOOGLE_SHEETS_ID=your_actual_sheet_id
GOOGLE_SHEETS_CREDENTIALS_FILE=credentials.json
WHALE_THRESHOLD_USD=500000
```

### **Step 2: Create Railway Account**

1. Go to [railway.app](https://railway.app)
2. Sign up with GitHub (recommended)
3. Verify your account

### **Step 3: Deploy to Railway**

**Option A: Deploy from Local Directory**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Initialize project
railway init

# Deploy
railway up
```

**Option B: Deploy from GitHub (Recommended)**
1. Create GitHub repository
2. Push your code (without .env and credentials.json)
3. Connect Railway to your GitHub repo
4. Auto-deploy on every push

### **Step 4: Set Environment Variables on Railway**

**In Railway Dashboard:**
1. Go to your project
2. Click "Variables" tab
3. Add each environment variable:

```
TELEGRAM_BOT_TOKEN = your_actual_token
TELEGRAM_CHAT_ID = your_actual_chat_id
ETHERSCAN_API_KEY = your_actual_key
BLOCKFROST_API_KEY = your_actual_key
GOOGLE_SHEETS_ID = your_actual_sheet_id
WHALE_THRESHOLD_USD = 500000
```

### **Step 5: Handle Google Credentials**

**For Railway, you have 2 options:**

**Option A: Base64 Encode (Recommended)**
```bash
# On your local machine:
base64 -i credentials.json

# Copy the output and add as Railway environment variable:
GOOGLE_CREDENTIALS_BASE64 = the_base64_string
```

Then update your code to decode it.

**Option B: Copy-Paste JSON**
```bash
# Add as Railway environment variable:
GOOGLE_CREDENTIALS_JSON = {"type":"service_account","project_id":"..."}
```

## 🔧 Code Changes for Railway

### **Update config.py for Cloud Deployment:**

Add this to handle cloud credentials:

```python
import base64
import json
import tempfile

def get_google_credentials_file():
    """Get Google credentials file path for cloud deployment"""
    
    # Try base64 encoded credentials first (Railway)
    base64_creds = os.getenv('GOOGLE_CREDENTIALS_BASE64')
    if base64_creds:
        # Decode and create temporary file
        creds_json = base64.b64decode(base64_creds).decode('utf-8')
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        temp_file.write(creds_json)
        temp_file.close()
        return temp_file.name
    
    # Try JSON string credentials (alternative)
    json_creds = os.getenv('GOOGLE_CREDENTIALS_JSON')
    if json_creds:
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        temp_file.write(json_creds)
        temp_file.close()
        return temp_file.name
    
    # Fallback to local file
    return os.getenv('GOOGLE_SHEETS_CREDENTIALS_FILE', 'credentials.json')

# Update the credentials file path
GOOGLE_SHEETS_CREDENTIALS_FILE = get_google_credentials_file()
```

## 📋 Pre-Deployment Checklist

**Local Testing:**
- [ ] `python main.py` runs without errors
- [ ] Telegram bot responds to `/check`
- [ ] Google Sheets gets updated
- [ ] All API connections working

**Security:**
- [ ] No credential files in git
- [ ] .env file is gitignored
- [ ] All secrets use environment variables
- [ ] credentials.json exists locally

**Railway Setup:**
- [ ] Railway account created
- [ ] All environment variables set
- [ ] Google credentials handled properly
- [ ] Deployment successful

## 🎯 Quick Start Commands

```bash
# 1. Restore credentials.json (download from Google Cloud)
# 2. Test locally
python main.py

# 3. Install Railway CLI
npm install -g @railway/cli

# 4. Deploy
railway login
railway init
railway up

# 5. Set environment variables in Railway dashboard
# 6. Your bot is now online 24/7!
```

## 🆘 Troubleshooting

**"Google credentials not found"**
- Ensure credentials.json exists locally
- For Railway: Set GOOGLE_CREDENTIALS_BASE64 variable

**"Telegram bot not responding"**
- Check TELEGRAM_BOT_TOKEN is correct
- Verify TELEGRAM_CHAT_ID is correct
- Check Railway logs for errors

**"API rate limits"**
- Verify API keys are correct
- Check if you've exceeded free tier limits

## ✅ Success Indicators

Your deployment is working when:
- ✅ Railway shows "Deployed" status
- ✅ Telegram bot responds to `/check`
- ✅ Daily whale digests arrive
- ✅ Google Sheets gets updated
- ✅ No errors in Railway logs

**Ready to deploy?** Railway is your best bet for 24/7 whale tracking! 🚀
