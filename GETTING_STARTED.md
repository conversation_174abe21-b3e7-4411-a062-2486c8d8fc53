# 🚀 Getting Started with Your Whale Tracker

Don't worry! I'll walk you through everything step by step. Your whale tracker is ready - we just need to set it up properly.

## 📁 What You Have Now

Here's what each file does (think of them as different parts of your whale-hunting machine):

### 🎯 **Core Files (The Main System)**
- **`main.py`** - The BOSS file. Run this to start everything
- **`config.py`** - Settings and configuration (like a settings menu)
- **`api_handler.py`** - Connects to blockchain APIs to find whales

### 🤖 **Communication Files**
- **`telegram_bot.py`** - Your personal whale assistant on Telegram
- **`sheets_handler.py`** - Saves whale data to Google Sheets

### 🪙 **Multi-Coin Files (The New Stuff)**
- **`multi_coin_config.py`** - List of all 8+ supported coins
- **`multi_chain_api.py`** - Advanced engine for multiple cryptocurrencies  
- **`coin_manager.py`** - Easy controls to enable/disable coins

### 🧪 **Testing Files**
- **`test_basic.py`** - Basic tests (works without API keys)
- **`test_multi_coin.py`** - Advanced multi-coin tests

## 🎯 **Your Next Steps (Choose Your Path)**

### 🟢 **OPTION 1: Quick Local Test (Recommended First)**

**Step 1: Test Without API Keys**
```bash
python test_basic.py
```
This shows you everything works! ✅

**Step 2: See Your Coin Options**
```bash
python coin_manager.py
```
This shows which coins you can track! 🪙

### 🟡 **OPTION 2: Full Local Setup (With Real Data)**

**Step 1: Get Your API Keys**

1. **Etherscan API Key** (Required for ETH and WLD):
   - Go to https://etherscan.io/apis
   - Create free account
   - Generate API key
   - Copy it

2. **Telegram Bot** (Required):
   - Message @BotFather on Telegram
   - Type `/newbot` and follow instructions
   - Copy your bot token
   - Message your bot, then visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - Find your chat ID in the response

**Step 2: Create Your .env File**
```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your keys:
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
ETHERSCAN_API_KEY=your_etherscan_key_here
```

**Step 3: Test With Real Data**
```bash
python main.py test
```

**Step 4: Start Your Whale Tracker**
```bash
python main.py
```
🎉 **You're now tracking whales!**

### 🔵 **OPTION 3: Google Sheets Setup (Optional)**

If you want to save whale data to spreadsheets:

1. Create a Google Cloud Project
2. Enable Google Sheets API
3. Create Service Account
4. Download `credentials.json`
5. Create a Google Sheet and share it with the service account email
6. Add the Sheet ID to your `.env` file

## 🌐 **What is Vercel? (Cloud Deployment)**

**Vercel** is like a "cloud computer" that runs your code 24/7 so you don't have to keep your laptop on.

### **Why Use Vercel?**
- ✅ Runs your whale tracker 24/7 automatically
- ✅ Free tier available
- ✅ No need to keep your computer on
- ✅ Automatic updates when you change code

### **How It Works:**
1. You upload your code to Vercel
2. Vercel runs it on their servers
3. Your whale tracker works 24/7 in the cloud
4. You still get Telegram messages as normal

### **Vercel Setup (When You're Ready):**

**Step 1: Prepare for Vercel**
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login
```

**Step 2: Create Vercel Configuration**
I'll help you create the config files when you're ready!

**Step 3: Deploy**
```bash
vercel --prod
```

## 🎯 **My Recommendation for You**

**Start with this order:**

1. **First**: Run `python test_basic.py` (works right now, no setup needed)
2. **Second**: Run `python coin_manager.py` (see your coin options)
3. **Third**: Get Telegram and Etherscan API keys
4. **Fourth**: Test with real data: `python main.py test`
5. **Fifth**: Run locally: `python main.py`
6. **Later**: Deploy to Vercel when you want 24/7 operation

## 🆘 **If You Get Stuck**

**Common Issues:**

1. **"Missing API keys"** - You need to create the `.env` file with your keys
2. **"Module not found"** - Make sure you're in the right directory and venv is activated
3. **"Permission denied"** - Make sure you're in the `whale-watch` folder

**Quick Fixes:**
```bash
# Make sure you're in the right place
cd c:\Users\<USER>\Documents\Projects\Whale-watch

# Make sure virtual environment is active
venv\Scripts\activate

# Test basic functionality
python test_basic.py
```

## 🎉 **What Happens When It's Running**

1. **Daily at 9 AM UTC**: You get a Telegram message with whale transactions
2. **Anytime**: You can send `/check` to your bot for instant whale updates
3. **Automatically**: All whale data gets saved to Google Sheets
4. **Multiple Coins**: Currently tracking BTC, ETH, and WLD (you can add more!)

## 🤔 **Questions?**

- **"Should I start with local testing?"** - YES! Start with `python test_basic.py`
- **"Do I need all the API keys?"** - No, start with just Telegram + Etherscan
- **"Should I deploy to Vercel now?"** - No, test locally first
- **"Can I add more coins?"** - YES! Use `coin_manager.py` to enable more

**Ready to start?** Run this command:
```bash
python test_basic.py
```

This will show you everything is working! 🚀
