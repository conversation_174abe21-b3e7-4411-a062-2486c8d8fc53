"""
📊 PORTFOLIO HISTORY READER - portfolio_history.py

This is your TRANSACTION HISTORY ANALYZER! This file:

1. 📖 Reads your existing Google Sheets transaction history
2. 🧮 Calculates weighted average cost basis for each coin
3. 📈 Tracks your purchase history and dates
4. 💰 Computes total invested amounts per coin
5. 🎯 Provides cost basis data for PnL calculations

WHAT IT DOES:
- Parses your Binance CSV export data from Google Sheets
- Calculates weighted average buy prices for each cryptocurrency
- Tracks total quantities purchased over time
- Provides historical context for performance calculations

HOW TO USE:
- Configure your Google Sheets with transaction history
- Run get_cost_basis() to get weighted averages
- Use the data for portfolio performance calculations

This reads your trading history to calculate true performance! 📈
"""

import logging
import gspread
from google.oauth2.service_account import Credentials
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import config
from portfolio_config import PORTFOLIO_SHEETS_CONFIG, get_enabled_portfolio_coins

logger = logging.getLogger(__name__)

class PortfolioHistoryReader:
    """Reads and analyzes portfolio transaction history from Google Sheets"""
    
    def __init__(self):
        self.client = None
        self.sheet = None
        self.worksheet = None
        self.cost_basis_cache = {}
        self.last_update = None
        self._initialize_sheets()
    
    def _initialize_sheets(self):
        """Initialize Google Sheets connection"""
        try:
            # Define the scope
            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]
            
            # Load credentials
            credentials = Credentials.from_service_account_file(
                config.GOOGLE_SHEETS_CREDENTIALS_FILE,
                scopes=scope
            )
            
            # Create client
            self.client = gspread.authorize(credentials)
            
            # Open the spreadsheet
            self.sheet = self.client.open_by_key(config.GOOGLE_SHEETS_ID)
            
            # Get the history worksheet
            sheet_name = PORTFOLIO_SHEETS_CONFIG['history_sheet_name']
            try:
                self.worksheet = self.sheet.worksheet(sheet_name)
                logger.info(f"Connected to portfolio history sheet: {sheet_name}")
            except gspread.WorksheetNotFound:
                logger.error(f"Portfolio history sheet '{sheet_name}' not found")
                self.worksheet = None
                
        except Exception as e:
            logger.error(f"Failed to initialize Google Sheets: {e}")
            self.client = None
            self.sheet = None
            self.worksheet = None
    
    def get_transaction_history(self) -> List[Dict]:
        """Get all transaction history from Google Sheets"""
        if not self.worksheet:
            logger.error("No worksheet available")
            return []
        
        try:
            # Get all records
            records = self.worksheet.get_all_records()
            
            # Filter and normalize the data
            transactions = []
            for record in records:
                # Skip empty rows
                if not any(record.values()):
                    continue
                
                # Normalize the transaction data
                transaction = self._normalize_transaction(record)
                if transaction:
                    transactions.append(transaction)
            
            logger.info(f"Loaded {len(transactions)} transactions from history")
            return transactions
            
        except Exception as e:
            logger.error(f"Error reading transaction history: {e}")
            return []
    
    def _normalize_transaction(self, record: Dict) -> Optional[Dict]:
        """Normalize a transaction record to standard format"""
        try:
            # Map common column names (adjust based on your sheet structure)
            column_mapping = {
                'date': ['Date', 'date', 'Date(UTC)', 'Time'],
                'coin': ['Coin', 'coin', 'Symbol', 'Asset', 'Currency', 'Pair'],  # Added 'Pair' for Binance
                'amount': ['Amount', 'amount', 'Quantity', 'Qty', 'Order Amount'],  # Added 'Order Amount' for Binance
                'price': ['Price', 'price', 'Rate', 'Unit Price', 'Order Price'],  # Added 'Order Price' for Binance
                'total': ['Total', 'total', 'Total Cost', 'Value', 'Trading Total'],  # Added 'Trading Total' for Binance
                'type': ['Type', 'type', 'Side', 'Operation', 'Transaction Type']
            }
            
            # Extract values using flexible column mapping
            transaction = {}
            for field, possible_columns in column_mapping.items():
                value = None
                for col in possible_columns:
                    if col in record and record[col]:
                        value = record[col]
                        break
                
                if value is not None:
                    transaction[field] = value
            
            # Validate required fields
            if not all(k in transaction for k in ['coin', 'amount']):
                return None
            
            # Clean and convert data types
            transaction['coin'] = str(transaction['coin']).upper().strip()
            transaction['amount'] = self._parse_number(transaction['amount'])

            if 'price' in transaction:
                transaction['price'] = self._parse_number(transaction['price'])
            elif 'total' in transaction:
                # Calculate price from total if not provided
                total = self._parse_number(transaction['total'])
                transaction['price'] = total / transaction['amount'] if transaction['amount'] > 0 else 0
            
            # Parse date
            if 'date' in transaction:
                transaction['date'] = self._parse_date(transaction['date'])
            
            # Determine transaction type (buy/sell)
            tx_type = transaction.get('type', '').lower()
            if 'buy' in tx_type or 'purchase' in tx_type:
                transaction['type'] = 'buy'
            elif 'sell' in tx_type:
                transaction['type'] = 'sell'
            else:
                # Default to buy for positive amounts
                transaction['type'] = 'buy' if transaction['amount'] > 0 else 'sell'
            
            return transaction
            
        except Exception as e:
            logger.warning(f"Error normalizing transaction: {e}")
            return None
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse date string to datetime object"""
        try:
            # Try common date formats including Binance format
            formats = [
                '%d.%m.%Y %H:%M',  # Binance format: "22.06.2021 09:24"
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(str(date_str), fmt)
                except ValueError:
                    continue
            
            # If all formats fail, return current date
            logger.warning(f"Could not parse date: {date_str}")
            return datetime.now()
            
        except Exception:
            return datetime.now()

    def _parse_number(self, value: str) -> float:
        """Parse a number from string, handling various formats including European"""
        if not value:
            return 0.0

        try:
            # Remove any currency symbols and whitespace
            cleaned = str(value).strip().replace('$', '').replace('€', '').replace('£', '')

            # For Binance European format, comma is always decimal separator
            # Examples: "0,07" -> 0.07, "274,26000" -> 274.26
            if ',' in cleaned:
                # Replace comma with dot for decimal separator
                cleaned = cleaned.replace(',', '.')

            return float(cleaned)
        except (ValueError, TypeError):
            return 0.0

    def calculate_cost_basis(self, coin: str = None) -> Dict[str, Dict]:
        """Calculate weighted average cost basis for coins"""
        transactions = self.get_transaction_history()
        
        if not transactions:
            return {}
        
        # Group transactions by coin
        coin_transactions = {}
        for tx in transactions:
            coin_symbol = tx['coin']
            if coin and coin_symbol != coin.upper():
                continue
                
            if coin_symbol not in coin_transactions:
                coin_transactions[coin_symbol] = []
            coin_transactions[coin_symbol].append(tx)
        
        # Calculate cost basis for each coin
        cost_basis_data = {}
        for coin_symbol, txs in coin_transactions.items():
            cost_basis_data[coin_symbol] = self._calculate_weighted_average(txs)
        
        # Cache the results
        self.cost_basis_cache = cost_basis_data
        self.last_update = datetime.now()
        
        return cost_basis_data
    
    def _calculate_weighted_average(self, transactions: List[Dict]) -> Dict:
        """Calculate weighted average cost basis for a coin"""
        buy_transactions = [tx for tx in transactions if tx.get('type') == 'buy']
        
        if not buy_transactions:
            return {
                'average_cost': 0,
                'total_quantity': 0,
                'total_invested': 0,
                'first_purchase': None,
                'last_purchase': None,
                'transaction_count': 0
            }
        
        total_cost = 0
        total_quantity = 0
        dates = []
        
        for tx in buy_transactions:
            amount = tx['amount']
            price = tx.get('price', 0)
            
            if price > 0 and amount > 0:
                total_cost += amount * price
                total_quantity += amount
                
                if 'date' in tx:
                    dates.append(tx['date'])
        
        average_cost = total_cost / total_quantity if total_quantity > 0 else 0
        
        return {
            'average_cost': round(average_cost, 6),
            'total_quantity': round(total_quantity, 6),
            'total_invested': round(total_cost, 2),
            'first_purchase': min(dates) if dates else None,
            'last_purchase': max(dates) if dates else None,
            'transaction_count': len(buy_transactions)
        }
    
    def get_cost_basis_summary(self) -> Dict[str, Dict]:
        """Get cost basis summary for all enabled coins"""
        if not self.cost_basis_cache or not self.last_update:
            self.calculate_cost_basis()
        
        # Filter for enabled coins only
        enabled_coins = get_enabled_portfolio_coins()
        summary = {}
        
        for coin in enabled_coins:
            if coin in self.cost_basis_cache:
                summary[coin] = self.cost_basis_cache[coin]
            else:
                # No history found for this coin
                summary[coin] = {
                    'average_cost': 0,
                    'total_quantity': 0,
                    'total_invested': 0,
                    'first_purchase': None,
                    'last_purchase': None,
                    'transaction_count': 0
                }
        
        return summary

# Global instance
portfolio_history = PortfolioHistoryReader()

# Convenience functions
def get_cost_basis(coin: str = None) -> Dict[str, Dict]:
    """Get cost basis data for specified coin or all coins"""
    return portfolio_history.calculate_cost_basis(coin)

def get_average_cost(coin: str) -> float:
    """Get weighted average cost for a specific coin"""
    cost_basis = portfolio_history.get_cost_basis_summary()
    return cost_basis.get(coin.upper(), {}).get('average_cost', 0)

def get_total_invested(coin: str) -> float:
    """Get total amount invested in a specific coin"""
    cost_basis = portfolio_history.get_cost_basis_summary()
    return cost_basis.get(coin.upper(), {}).get('total_invested', 0)

if __name__ == "__main__":
    # Test the history reader
    try:
        print("📊 Testing Portfolio History Reader...")
        
        cost_basis = get_cost_basis()
        
        if cost_basis:
            print(f"\n✅ Found cost basis data for {len(cost_basis)} coins:")
            for coin, data in cost_basis.items():
                print(f"  {coin}: Avg Cost ${data['average_cost']:.4f}, Total Invested ${data['total_invested']:.2f}")
        else:
            print("❌ No cost basis data found. Check your Google Sheets configuration.")
            
    except Exception as e:
        print(f"❌ Error testing portfolio history: {e}")
