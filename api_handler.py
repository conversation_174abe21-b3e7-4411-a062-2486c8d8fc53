"""
🌐 API HANDLER - api_handler.py

This is your CONNECTION to the blockchain world! This file:

1. 🔍 Fetches current balances from your wallet addresses
2. 💰 Gets current crypto prices from CoinGecko
3. 🚦 Manages rate limits (so you don't hit API limits)
4. 🪙 Supports multiple coins (BTC, ETH, WLD, etc.)
5. 📊 Provides portfolio performance data

WHAT IT DOES:
- Connects to Etherscan, Blockstream, and other APIs
- Fetches your current balances from all addresses
- Respects free API tier limits
- Works with multiple blockchain networks

HOW TO USE:
- You don't run this directly
- Other modules use this to get portfolio data
- Automatically fetches balances from your configured addresses

This is your portfolio data pipeline! 📊
"""

import requests
import time
import logging
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import config

# Legacy multi-chain and learning features removed
# Portfolio tracker uses simplified balance fetching approach
MULTI_CHAIN_AVAILABLE = False
ENHANCED_FEATURES_AVAILABLE = False

# Import portfolio configuration
try:
    from portfolio_config import (
        get_all_portfolio_addresses,
        get_enabled_portfolio_coins,
        get_coingecko_ids,
        get_portfolio_addresses_by_chain
    )
    PORTFOLIO_CONFIG_AVAILABLE = True
    logging.info("Portfolio configuration available")
except ImportError:
    PORTFOLIO_CONFIG_AVAILABLE = False
    logging.warning("Portfolio configuration not available")

logger = logging.getLogger(__name__)

class APIHandler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Portfolio-Tracker/1.0'
        })

        # Portfolio tracker uses simplified approach - no multi-chain handler needed
        self.multi_chain_handler = None
        
    def _rate_limited_request(self, url: str, params: Dict, rate_limit: float) -> Optional[Dict]:
        """Make a rate-limited API request"""
        try:
            time.sleep(rate_limit)  # Respect rate limits
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {url} - {e}")
            return None
    
    def get_current_prices(self) -> Dict[str, float]:
        """Get current prices for all portfolio coins from Binance API (free!)"""
        price_data = self.get_prices_and_changes()
        return price_data['prices']

    def get_daily_changes(self) -> Dict[str, float]:
        """Get 24h price changes for all portfolio coins from Binance API"""
        price_data = self.get_prices_and_changes()
        return price_data['daily_changes']

    def get_prices_and_changes(self) -> Dict[str, Dict[str, float]]:
        """Get both current prices and 24h changes from Binance API"""
        try:
            # Binance API endpoint for 24hr ticker statistics (includes price + 24h change)
            url = "https://api1.binance.com/api/v3/ticker/24hr"

            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            data = response.json()

            # Create price and change lookup from Binance
            binance_data = {}
            for item in data:
                binance_data[item['symbol']] = {
                    'price': float(item['lastPrice']),
                    'change_percent': float(item['priceChangePercent'])
                }

            # Map your portfolio coins to Binance symbols (only major coins)
            coin_mapping = {
                'BTC': 'BTCUSDT',
                'ETH': 'ETHUSDT',
                'ADA': 'ADAUSDT',
                'NEO': 'NEOUSDT',
                'BNB': 'BNBUSDT',
                'ONE': 'ONEUSDT',
                'WLD': 'WLDUSDT',
                'USDC': 'USDCUSDT',
                'USDT': 'USDTUSDT',  # USDT = ~$1
                'TON': 'TONUSDT',
                'GAS': 'GASUSDT'
                # Removed: SHIB, FUN, VTHO, NFP (disabled in portfolio_config.py)
            }

            # Get prices and changes for your coins
            prices = {}
            daily_changes = {}
            for coin, binance_symbol in coin_mapping.items():
                if binance_symbol in binance_data:
                    prices[coin] = binance_data[binance_symbol]['price']
                    daily_changes[coin] = binance_data[binance_symbol]['change_percent']
                else:
                    prices[coin] = 0
                    daily_changes[coin] = 0

            # Special cases for stablecoins
            prices['USDC'] = 1.0  # USDC is always ~$1
            prices['USDT'] = 1.0  # USDT is always ~$1
            daily_changes['USDC'] = 0.0  # Stablecoins don't change much
            daily_changes['USDT'] = 0.0

            logger.info(f"Fetched {len(prices)} prices and daily changes from Binance")
            return {
                'prices': prices,
                'daily_changes': daily_changes
            }

        except Exception as e:
            logger.error(f"Failed to fetch prices from Binance: {e}")
            logger.warning("Using fallback prices")
            return {
                'BTC': 65000, 'ETH': 3500, 'ADA': 0.35, 'NEO': 12,
                'BNB': 600, 'ONE': 0.015, 'WLD': 2.5, 'USDC': 1.0
            }
    
    def calculate_dynamic_thresholds(self, prices: Dict[str, float]) -> Dict[str, float]:
        """Calculate minimum amounts based on current prices to meet USD threshold"""
        eth_price = prices.get('ETH', 3000)
        btc_price = prices.get('BTC', 60000)
        
        return {
            'ETH': max(config.WHALE_THRESHOLD_USD / eth_price, config.MIN_ETH_AMOUNT),
            'BTC': max(config.WHALE_THRESHOLD_USD / btc_price, config.MIN_BTC_AMOUNT)
        }
    
    def get_eth_whale_transactions(self, min_eth_amount: float, hours_back: int = 24) -> List[Dict]:
        """
        Get large ETH transactions using Etherscan API
        Uses the account transactions endpoint for known whale addresses
        """
        whale_transactions = []
        
        # Known whale addresses to monitor (exchanges, large holders)
        whale_addresses = [
            '******************************************',  # Binance 14
            '******************************************',  # Binance 15
            '******************************************',  # Binance 16
            '******************************************',  # Binance 17
            '******************************************',  # Binance 18
            '******************************************',  # Binance 19
            '******************************************',  # Binance 20
            '******************************************',  # Binance 8
        ]
        
        end_block = self._get_latest_block_number()
        if not end_block:
            return whale_transactions
            
        # Calculate approximate start block (assuming 12 second block time)
        blocks_back = (hours_back * 3600) // 12
        start_block = max(end_block - blocks_back, end_block - 10000)  # Limit to prevent too many results
        
        for address in whale_addresses[:3]:  # Limit to first 3 addresses to conserve API calls
            try:
                url = config.ETHERSCAN_BASE_URL
                params = {
                    'chainid': 1,  # Ethereum mainnet
                    'module': 'account',
                    'action': 'txlist',
                    'address': address,
                    'startblock': start_block,
                    'endblock': end_block,
                    'page': 1,
                    'offset': 100,  # Limit results
                    'sort': 'desc',
                    'apikey': config.ETHERSCAN_API_KEY
                }
                
                data = self._rate_limited_request(url, params, config.ETHERSCAN_RATE_LIMIT)
                
                if data and data.get('status') == '1':
                    transactions = data.get('result', [])
                    
                    for tx in transactions:
                        try:
                            # Convert wei to ETH
                            eth_amount = float(tx.get('value', 0)) / 1e18
                            
                            if eth_amount >= min_eth_amount:
                                whale_transactions.append({
                                    'blockchain': 'ETH',
                                    'hash': tx.get('hash'),
                                    'from': tx.get('from'),
                                    'to': tx.get('to'),
                                    'amount': eth_amount,
                                    'currency': 'ETH',
                                    'timestamp': datetime.fromtimestamp(int(tx.get('timeStamp', 0))),
                                    'block_number': tx.get('blockNumber')
                                })
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error processing ETH transaction: {e}")
                            continue
                            
            except Exception as e:
                logger.error(f"Error fetching transactions for address {address}: {e}")
                continue
        
        return whale_transactions
    
    def _get_latest_block_number(self) -> Optional[int]:
        """Get the latest Ethereum block number"""
        url = config.ETHERSCAN_BASE_URL
        params = {
            'chainid': 1,  # Ethereum mainnet
            'module': 'proxy',
            'action': 'eth_blockNumber',
            'apikey': config.ETHERSCAN_API_KEY
        }
        
        data = self._rate_limited_request(url, params, config.ETHERSCAN_RATE_LIMIT)
        
        if data and data.get('result'):
            try:
                return int(data['result'], 16)  # Convert hex to int
            except ValueError:
                return None
        return None
    
    def get_btc_whale_transactions(self, min_btc_amount: float, hours_back: int = 24) -> List[Dict]:
        """
        Get large BTC transactions using Blockchain.info API
        Note: This is more limited than Etherscan for historical data
        """
        whale_transactions = []
        
        try:
            # Get recent blocks (limited approach due to API constraints)
            url = f"{config.BLOCKCHAIN_INFO_BASE_URL}/latestblock"
            
            data = self._rate_limited_request(url, {}, config.BLOCKCHAIN_INFO_RATE_LIMIT)
            
            if not data:
                return whale_transactions
                
            latest_block_height = data.get('height', 0)
            
            # Check last few blocks for large transactions
            blocks_to_check = min(6, hours_back // 2)  # Roughly 2 hours per 6 blocks
            
            for i in range(blocks_to_check):
                block_height = latest_block_height - i
                block_url = f"{config.BLOCKCHAIN_INFO_BASE_URL}/rawblock/{block_height}"
                
                block_data = self._rate_limited_request(block_url, {}, config.BLOCKCHAIN_INFO_RATE_LIMIT)
                
                if block_data and 'tx' in block_data:
                    for tx in block_data['tx'][:20]:  # Limit to first 20 transactions per block
                        try:
                            # Calculate total output value
                            total_output = sum(output.get('value', 0) for output in tx.get('out', []))
                            btc_amount = total_output / 1e8  # Convert satoshis to BTC
                            
                            if btc_amount >= min_btc_amount:
                                whale_transactions.append({
                                    'blockchain': 'BTC',
                                    'hash': tx.get('hash'),
                                    'from': 'Multiple' if len(tx.get('inputs', [])) > 1 else 'N/A',
                                    'to': 'Multiple' if len(tx.get('out', [])) > 1 else 'N/A',
                                    'amount': btc_amount,
                                    'currency': 'BTC',
                                    'timestamp': datetime.fromtimestamp(tx.get('time', 0)),
                                    'block_number': block_height
                                })
                        except (ValueError, TypeError, KeyError) as e:
                            logger.warning(f"Error processing BTC transaction: {e}")
                            continue
                            
        except Exception as e:
            logger.error(f"Error fetching BTC transactions: {e}")
        
        return whale_transactions
    
    def get_all_whale_transactions(self, hours_back: int = 24) -> List[Dict]:
        """Get whale transactions from all enabled chains with enhanced features"""

        # Use multi-chain handler if available
        if self.multi_chain_handler:
            raw_transactions = self.multi_chain_handler.get_all_whale_transactions(hours_back)

            # Learning features removed for portfolio tracker
            # Return raw transactions without enhancement
            return raw_transactions

        # Fallback to legacy ETH + BTC only mode
        logger.info(f"Using legacy mode - fetching whale transactions from last {hours_back} hours")

        # Get current prices
        prices = self.get_current_prices()
        logger.info(f"Current prices - ETH: ${prices['ETH']:.2f}, BTC: ${prices['BTC']:.2f}")

        # Calculate dynamic thresholds
        thresholds = self.calculate_dynamic_thresholds(prices)
        logger.info(f"Whale thresholds - ETH: {thresholds['ETH']:.2f}, BTC: {thresholds['BTC']:.4f}")

        all_transactions = []

        # Get ETH transactions
        eth_transactions = self.get_eth_whale_transactions(thresholds['ETH'], hours_back)
        logger.info(f"Found {len(eth_transactions)} ETH whale transactions")

        # Get BTC transactions
        btc_transactions = self.get_btc_whale_transactions(thresholds['BTC'], hours_back)
        logger.info(f"Found {len(btc_transactions)} BTC whale transactions")

        all_transactions.extend(eth_transactions)
        all_transactions.extend(btc_transactions)

        # Add USD values and current prices
        for tx in all_transactions:
            current_price = prices.get(tx['currency'], 0)
            tx['current_price'] = current_price
            tx['usd_value'] = tx['amount'] * current_price

        # Sort by USD value (largest first)
        all_transactions.sort(key=lambda x: x.get('usd_value', 0), reverse=True)

        # Learning features removed for portfolio tracker
        logger.info(f"Total whale transactions found: {len(all_transactions)}")
        return all_transactions

    def get_enabled_coins_info(self) -> Dict[str, Dict]:
        """Get information about enabled coins"""
        if self.multi_chain_handler:
            return self.multi_chain_handler.get_enabled_coins_info()
        else:
            # Legacy mode - only ETH and BTC
            return {
                'ETH': {'name': 'Ethereum', 'chain_type': 'evm', 'api_provider': 'etherscan', 'has_handler': True},
                'BTC': {'name': 'Bitcoin', 'chain_type': 'bitcoin', 'api_provider': 'blockchain.info', 'has_handler': True}
            }

    # Portfolio-specific methods
    def get_portfolio_balances(self) -> Dict[str, float]:
        """Get current balances for all portfolio addresses and exchanges"""
        balances = {}

        # Get blockchain balances
        if PORTFOLIO_CONFIG_AVAILABLE:
            addresses = get_all_portfolio_addresses()

            for address_config in addresses:
                try:
                    balance = self._get_address_balance(
                        address_config.address,
                        address_config.chain
                    )

                    coin = address_config.chain.upper()
                    if coin not in balances:
                        balances[coin] = 0
                    balances[coin] += balance

                    logger.info(f"Balance for {address_config.label} ({coin}): {balance}")

                except Exception as e:
                    logger.error(f"Error fetching balance for {address_config.label}: {e}")
                    continue
        else:
            logger.warning("Portfolio configuration not available")

        # Get Binance exchange balances
        try:
            binance_balances = self.get_binance_balances()
            for asset, balance in binance_balances.items():
                if asset in balances:
                    balances[asset] += balance
                else:
                    balances[asset] = balance
                logger.info(f"Binance {asset} balance: {balance}")

        except Exception as e:
            logger.error(f"Error fetching Binance balances: {e}")

        return balances

    def _get_address_balance(self, address: str, chain: str) -> float:
        """Get balance for a specific address on a specific chain"""
        chain = chain.upper()

        if chain == 'BTC':
            return self._get_btc_balance(address)
        elif chain == 'ETH':
            return self._get_eth_balance(address)
        elif chain == 'ADA':
            return self._get_ada_balance(address)
        elif chain == 'USDC':
            return self._get_usdc_balance(address)
        else:
            logger.warning(f"Balance fetching not implemented for {chain}")
            return 0.0

    def _get_btc_balance(self, address: str) -> float:
        """Get BTC balance for an address"""
        try:
            url = f"https://blockchain.info/q/addressbalance/{address}"
            response = self._rate_limited_request(url, rate_limit=config.BLOCKCHAIN_INFO_RATE_LIMIT)

            if response:
                # Blockchain.info returns balance in satoshis
                satoshis = int(response)
                return satoshis / 100000000  # Convert to BTC
            return 0.0

        except Exception as e:
            logger.error(f"Error fetching BTC balance for {address}: {e}")
            return 0.0

    def _get_eth_balance(self, address: str) -> float:
        """Get ETH balance for an address using Etherscan V2 API"""
        try:
            # Using Etherscan V2 API with chainid parameter
            url = config.ETHERSCAN_BASE_URL
            params = {
                'chainid': 1,  # Ethereum mainnet
                'module': 'account',
                'action': 'balance',
                'address': address,
                'tag': 'latest',
                'apikey': config.ETHERSCAN_API_KEY
            }

            response = self._rate_limited_request(url, params, config.ETHERSCAN_RATE_LIMIT)

            if response and response.get('status') == '1':
                wei = int(response['result'])
                eth_balance = wei / 1e18  # Convert wei to ETH
                logger.info(f"ETH balance for {address[:10]}...: {eth_balance:.6f} ETH")
                return eth_balance
            else:
                error_msg = response.get('message', 'Unknown error') if response else 'No response'
                logger.warning(f"Etherscan API error for {address[:10]}...: {error_msg}")
                return 0.0

        except Exception as e:
            logger.error(f"Error fetching ETH balance for {address}: {e}")
            return 0.0

    def _get_ada_balance(self, address: str) -> float:
        """Get ADA balance for a Cardano stake address using Blockfrost API"""
        try:
            if not config.BLOCKFROST_API_KEY:
                logger.warning("BLOCKFROST_API_KEY not configured")
                return 0.0

            # Blockfrost API endpoint for stake address info
            url = f"https://cardano-mainnet.blockfrost.io/api/v0/accounts/{address}"
            headers = {
                'project_id': config.BLOCKFROST_API_KEY
            }

            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                # Balance is in lovelace (1 ADA = 1,000,000 lovelace)
                balance_lovelace = int(data.get('controlled_amount', 0))
                balance_ada = balance_lovelace / 1_000_000
                logger.info(f"ADA balance for {address[:20]}...: {balance_ada}")
                return balance_ada
            else:
                logger.error(f"Blockfrost API error {response.status_code}: {response.text}")
                return 0.0

        except Exception as e:
            logger.error(f"Error fetching ADA balance for {address}: {e}")
            return 0.0

    def _get_usdc_balance(self, address: str) -> float:
        """Get USDC balance for an address (ERC-20 token)"""
        try:
            # USDC contract address on Ethereum (official Circle contract)
            usdc_contract = "******************************************"  # Official USDC contract

            url = config.ETHERSCAN_BASE_URL
            params = {
                'chainid': 1,  # Ethereum mainnet
                'module': 'account',
                'action': 'tokenbalance',
                'contractaddress': usdc_contract,
                'address': address,
                'tag': 'latest',
                'apikey': config.ETHERSCAN_API_KEY
            }

            response = self._rate_limited_request(url, params, config.ETHERSCAN_RATE_LIMIT)

            if response and response.get('status') == '1':
                # USDC has 6 decimals
                balance_raw = int(response['result'])
                usdc_balance = balance_raw / 1e6  # Convert to USDC
                logger.info(f"USDC balance for {address[:10]}...: {usdc_balance:.2f} USDC")
                return usdc_balance
            else:
                error_msg = response.get('message', 'Unknown error') if response else 'No response'
                logger.warning(f"Etherscan USDC API error for {address[:10]}...: {error_msg}")
                return 0.0

        except Exception as e:
            logger.error(f"Error fetching USDC balance for {address}: {e}")
            return 0.0

    def get_binance_balances(self) -> Dict[str, float]:
        """Get balances from Binance exchange"""
        try:
            if not config.BINANCE_API_KEY or not config.BINANCE_API_SECRET:
                logger.warning("Binance API credentials not configured")
                return {}

            # Binance API endpoint
            url = "https://api1.binance.com/api/v3/account"
            timestamp = int(time.time() * 1000)

            # Create query string
            query_string = f"timestamp={timestamp}"

            # Create signature
            signature = hmac.new(
                config.BINANCE_API_SECRET.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # Add signature to query
            query_string += f"&signature={signature}"

            headers = {
                'X-MBX-APIKEY': config.BINANCE_API_KEY
            }

            response = requests.get(f"{url}?{query_string}", headers=headers)

            if response.status_code == 200:
                data = response.json()
                balances = {}

                for balance in data.get('balances', []):
                    asset = balance['asset']
                    free = float(balance['free'])
                    locked = float(balance['locked'])
                    total = free + locked

                    if total > 0:  # Only include non-zero balances
                        balances[asset] = total

                logger.info(f"Retrieved {len(balances)} non-zero Binance balances")
                return balances
            else:
                logger.error(f"Binance API error: {response.status_code} - {response.text}")
                return {}

        except Exception as e:
            logger.error(f"Error fetching Binance balances: {e}")
            return {}

    def get_portfolio_performance(self) -> Dict:
        """Get complete portfolio performance data"""
        try:
            # Get current balances
            balances = self.get_portfolio_balances()

            # Get current prices
            prices = self.get_current_prices()

            # Calculate portfolio data
            portfolio_data = {
                'timestamp': datetime.now().isoformat(),
                'balances': balances,
                'prices': prices,
                'total_value_usd': 0
            }

            # Calculate total portfolio value
            for coin, balance in balances.items():
                if coin in prices and balance > 0:
                    value = balance * prices[coin]
                    portfolio_data['total_value_usd'] += value
                    portfolio_data[f'{coin}_value_usd'] = value

            logger.info(f"Portfolio total value: ${portfolio_data['total_value_usd']:.2f}")
            return portfolio_data

        except Exception as e:
            logger.error(f"Error getting portfolio performance: {e}")
            return {}
