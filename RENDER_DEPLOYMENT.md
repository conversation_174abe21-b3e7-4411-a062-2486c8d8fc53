# 🚀 RENDER.COM DEPLOYMENT - FREE 24/7 WHALE TRACKER!

## 🎯 Why Render.com is PERFECT for Your Whale Tracker

### **✅ Render Advantages:**
- 🆓 **Completely FREE** for background services
- ⏰ **750 hours/month** free (enough for 24/7 operation)
- 🔄 **Auto-restart** if your bot crashes
- 🌐 **24/7 uptime** - perfect for whale tracking
- 💳 **No credit card required** for free tier
- 🔧 **Easy Python deployment**
- 📊 **Built-in logging and monitoring**

### **vs Railway:**
- ❌ Railway: $5/month after 30 days
- ✅ Render: FREE forever (with limits)

### **vs Vercel:**
- ❌ Vercel: Not designed for 24/7 background processes
- ✅ Render: Perfect for background services

## 🚀 STEP-BY-STEP RENDER DEPLOYMENT

### **Step 1: Fix Credentials File Name**

I noticed you have `credentials.josn.json` (typo). Let's fix this:

```bash
# Rename the file to correct name
mv credentials.josn.json credentials.json
```

### **Step 2: Test Everything Locally**

```bash
# Test your whale tracker
python main.py

# Test Telegram bot (send /check command)
# Verify Google Sheets integration
```

### **Step 3: Create Render Account**

1. Go to [render.com](https://render.com)
2. Click "Get Started for Free"
3. Sign up with GitHub (recommended)
4. Verify your email

### **Step 4: Prepare for Deployment**

**Option A: Deploy from GitHub (Recommended)**

```bash
# Initialize git (if not already done)
git init

# Add all files (credentials.json is gitignored)
git add .

# Commit
git commit -m "Whale tracker ready for deployment"

# Create GitHub repository and push
# (Follow GitHub's create repository instructions)
```

**Option B: Deploy Directly (Alternative)**
- Upload code directly to Render
- Less convenient for updates

### **Step 5: Create Render Service**

1. **In Render Dashboard:**
   - Click "New +"
   - Select "Background Worker"
   - Connect your GitHub repository (or upload code)

2. **Configure Service:**
   ```
   Name: whale-tracker
   Environment: Python 3
   Build Command: pip install -r requirements.txt
   Start Command: python main.py
   ```

### **Step 6: Encode Google Credentials**

**You need to convert credentials.json to base64:**

**On Windows:**
```cmd
certutil -encode credentials.json temp.txt
# Open temp.txt, copy content between -----BEGIN----- and -----END----- lines
# Remove line breaks to make it one long string
```

**On Mac/Linux:**
```bash
base64 -i credentials.json
# Copy the output
```

### **Step 7: Set Environment Variables in Render**

**In Render Service → Environment:**

Add each variable:

```
TELEGRAM_BOT_TOKEN = your_actual_bot_token
TELEGRAM_CHAT_ID = your_actual_chat_id
ETHERSCAN_API_KEY = your_actual_etherscan_key
BLOCKFROST_API_KEY = your_actual_blockfrost_key
GOOGLE_SHEETS_ID = your_actual_sheets_id
GOOGLE_CREDENTIALS_BASE64 = your_base64_encoded_credentials
WHALE_THRESHOLD_USD = 500000
```

### **Step 8: Deploy!**

1. Click "Create Web Service" or "Deploy"
2. Render will build and start your service
3. Check logs for any errors
4. Test your Telegram bot with `/check`

## 📋 Pre-Deployment Checklist

**Local Testing:**
- [ ] `python main.py` runs without errors
- [ ] Telegram bot responds to `/check`
- [ ] Google Sheets gets updated
- [ ] `credentials.json` exists (not `credentials.josn.json`)

**Render Setup:**
- [ ] Render account created
- [ ] GitHub repository connected (if using)
- [ ] All environment variables set
- [ ] Google credentials base64 encoded
- [ ] Service configured correctly

## 🆘 Troubleshooting

### **Common Issues:**

**1. "credentials.json not found"**
```bash
# Fix: Ensure file is named correctly
mv credentials.josn.json credentials.json
```

**2. "Google credentials error"**
```bash
# Fix: Check base64 encoding
# Ensure GOOGLE_CREDENTIALS_BASE64 is set correctly
```

**3. "Telegram bot not responding"**
```bash
# Fix: Check environment variables
# Verify TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
```

**4. "Build failed"**
```bash
# Fix: Check requirements.txt
# Ensure all dependencies are listed
```

### **Debug Commands:**

**Check Render Logs:**
- Go to Render Dashboard
- Click your service
- View "Logs" tab

**Test Locally:**
```bash
python main.py
```

## ✅ Success Indicators

Your deployment is working when:
- ✅ Render shows "Live" status
- ✅ Telegram bot responds to `/check`
- ✅ Daily whale digests arrive
- ✅ Google Sheets gets updated
- ✅ No errors in Render logs

## 🎉 After Successful Deployment

**Your whale tracker will:**
- 🔄 Run 24/7 automatically
- 📱 Send daily Telegram digests
- 📊 Update Google Sheets with whale data
- 🔄 Auto-restart if it crashes
- 📈 Track whales across multiple blockchains

## 💡 Next Steps After Deployment

1. **Monitor for a few days** - check logs and functionality
2. **Add more exchange addresses** to `exchange_addresses.json`
3. **Customize filtering rules** based on collected data
4. **Consider upgrading** to paid plan if you hit limits

## 🆓 Render Free Tier Limits

**What you get FREE:**
- ✅ 750 hours/month (enough for 24/7)
- ✅ 512MB RAM
- ✅ 0.1 CPU
- ✅ Auto-sleep after 15 minutes of inactivity (but restarts automatically)

**Perfect for your whale tracker!** 🐋

## 🚀 Ready to Deploy?

1. Fix `credentials.josn.json` → `credentials.json`
2. Test locally one more time
3. Create Render account
4. Follow the steps above
5. Your whale tracker goes live! 🎉

**Need help with any step?** The process is straightforward, and Render's free tier is perfect for your use case!
