#!/usr/bin/env python3
"""
🧪 API CONNECTION TESTER

This script tests all the API connections to make sure everything is working:
- Cardano (Blockfrost API)
- Harmony (RPC API)
- Google Sheets (if configured)

Run this before running the main whale tracker to verify everything works!
"""

import os
import requests
import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def test_cardano_api():
    """Test Cardano Blockfrost API connection"""
    print("🔍 Testing Cardano (Blockfrost) API...")
    
    api_key = os.getenv('BLOCKFROST_API_KEY')
    if not api_key:
        print("❌ BLOCKFROST_API_KEY not found in environment variables")
        print("   Get your free API key from: https://blockfrost.io")
        return False
    
    try:
        headers = {'project_id': api_key}
        url = "https://cardano-mainnet.blockfrost.io/api/v0/blocks/latest"
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Cardano API working! Latest block: {data.get('height', 'unknown')}")
            return True
        else:
            print(f"❌ Cardano API error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Cardano API connection failed: {e}")
        return False

def test_harmony_api():
    """Test Harmony RPC API connection"""
    print("🔍 Testing Harmony RPC API...")
    
    try:
        url = "https://api.harmony.one"
        payload = {
            "jsonrpc": "2.0",
            "method": "hmyv2_latestHeader",
            "params": [],
            "id": 1
        }
        
        response = requests.post(url, json=payload, timeout=10)
        
        if response.status_code == 200:
            data = response.json()

            if 'result' in data and data['result']:
                # Handle different response formats
                result = data['result']
                if isinstance(result, dict) and 'blockNumber' in result:
                    block_number = result['blockNumber']
                    # Handle both int and hex string formats
                    if isinstance(block_number, str):
                        block_number = int(block_number, 16)
                    # If it's already an int, use it directly
                elif isinstance(result, str):
                    block_number = int(result, 16)
                else:
                    print(f"❌ Unexpected result format: {result}")
                    return False
                print(f"✅ Harmony API working! Latest block: {block_number}")
                return True
            else:
                print(f"❌ Harmony API returned invalid data: {data}")
                return False
        else:
            print(f"❌ Harmony API error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Harmony API connection failed: {e}")
        return False

def test_google_sheets():
    """Test Google Sheets connection"""
    print("🔍 Testing Google Sheets connection...")
    
    sheets_id = os.getenv('GOOGLE_SHEETS_ID')
    if not sheets_id:
        print("⚠️  GOOGLE_SHEETS_ID not set - skipping Google Sheets test")
        print("   This is optional for whale tracking functionality")
        return True
    
    try:
        # Try to import and test sheets handler
        from sheets_handler import SheetsHandler
        
        sheets = SheetsHandler()
        if sheets.sheet:
            print("✅ Google Sheets connection working!")
            return True
        else:
            print("❌ Google Sheets connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Google Sheets test failed: {e}")
        return False

def test_binance_api():
    """Test Binance API for price fetching"""
    print("🔍 Testing Binance API for price data...")
    
    try:
        url = "https://api.binance.com/api/v3/ticker/price"
        params = {"symbols": '["BTCUSDT","ETHUSDT","WLDUSDT","ADAUSDT","ONEUSDT"]'}
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if len(data) >= 4:  # Should have at least 4 coins
                print(f"✅ Binance API working! Got prices for {len(data)} coins")
                return True
            else:
                print(f"❌ Binance API returned insufficient data: {len(data)} coins")
                return False
        else:
            print(f"❌ Binance API error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Binance API connection failed: {e}")
        return False

def main():
    """Run all API tests"""
    print("🚀 Whale Tracker API Connection Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now()}")
    print()
    
    tests = [
        ("Binance Price API", test_binance_api),
        ("Cardano API", test_cardano_api),
        ("Harmony API", test_harmony_api),
        ("Google Sheets", test_google_sheets),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your whale tracker is ready to run!")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Check the errors above.")
        print("   The whale tracker may still work with limited functionality.")

if __name__ == "__main__":
    main()
