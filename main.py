"""
📊 PORTFOLIO TRACKER MAIN - main.py

This is the HEART of your portfolio tracker! This file:

1. 🤖 Starts the Telegram bot (so you can use /check commands)
2. ⏰ Schedules daily portfolio checks at 9 AM UTC
3. 🔗 Connects all the other modules together
4. 📊 Sends you daily portfolio digests via Telegram
5. 🛡️ Handles errors and keeps everything running smoothly

WHAT IT DOES:
- Runs 24/7 to monitor your portfolio performance
- Sends you a daily summary of your crypto holdings
- Responds to your Telegram commands like /check
- Saves all portfolio data to Google Sheets automatically

HOW TO USE:
- Run: python main.py (starts the whole system)
- Run: python main.py test (tests everything first)

This is what you'll run to start your portfolio tracker!
"""

import asyncio
import logging
import logging.config
import schedule
import time
from datetime import datetime
import signal
import sys
from typing import Optional

import config
from api_handler import APIHandler
from telegram_bot import PortfolioTrackerBot
from sheets_handler import SheetsHandler

# Import enhanced features
try:
    from price_tracker import price_tracker
    PRICE_TRACKING_AVAILABLE = True
    logging.info("Price tracking available")
except ImportError:
    PRICE_TRACKING_AVAILABLE = False
    logging.warning("Price tracking not available")

# Configure logging
logging.config.dictConfig(config.LOGGING_CONFIG)
logger = logging.getLogger(__name__)

class PortfolioTracker:
    def __init__(self):
        self.api_handler = APIHandler()
        self.telegram_bot = PortfolioTrackerBot()
        self.sheets_handler = SheetsHandler()
        self.running = False
        self.bot_task = None

    async def daily_portfolio_check(self):
        """Perform daily portfolio performance check and send digest"""
        try:
            logger.info("Starting daily portfolio check")

            # Get portfolio performance data
            from portfolio_calculator import get_portfolio_digest
            portfolio_digest = get_portfolio_digest()

            if portfolio_digest and "Unable to generate" not in portfolio_digest:
                # TODO: Save portfolio snapshot to Google Sheets
                logger.info(f"Generated portfolio digest successfully")

                # Send Telegram digest
                if self.telegram_bot.application:
                    await self.telegram_bot.send_portfolio_digest(config.TELEGRAM_CHAT_ID, portfolio_digest)
            else:
                logger.info("No portfolio data available for daily digest")
                
                # Send "no whales" message
                if self.telegram_bot.application:
                    current_date = datetime.now().strftime("%Y-%m-%d")
                    no_whales_message = f"🐋 Daily Whale Digest - {current_date}\n\nNo whale transactions found in the last 24 hours."
                    
                    await self.telegram_bot.application.bot.send_message(
                        chat_id=config.TELEGRAM_CHAT_ID,
                        text=no_whales_message
                    )
            
            logger.info("Daily whale check completed")
            
        except Exception as e:
            logger.error(f"Error in daily whale check: {e}")
            
            # Send error notification
            try:
                if self.telegram_bot.application:
                    await self.telegram_bot.application.bot.send_message(
                        chat_id=config.TELEGRAM_CHAT_ID,
                        text="❌ Error during daily whale check. Please check logs."
                    )
            except Exception as send_error:
                logger.error(f"Failed to send error notification: {send_error}")
    
    def schedule_daily_check(self):
        """Schedule the daily whale check"""
        schedule.every().day.at(config.DAILY_CHECK_TIME).do(
            lambda: asyncio.create_task(self.daily_whale_check())
        )
        logger.info(f"Scheduled daily whale check at {config.DAILY_CHECK_TIME}")
    
    async def start_bot(self):
        """Start the Telegram bot"""
        try:
            logger.info("Starting Telegram bot...")
            self.telegram_bot.setup_bot()
            await self.telegram_bot.start_polling()
            logger.info("Telegram bot started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Telegram bot: {e}")
            raise
    
    async def stop_bot(self):
        """Stop the Telegram bot"""
        try:
            await self.telegram_bot.stop_polling()
            logger.info("Telegram bot stopped")
        except Exception as e:
            logger.error(f"Error stopping Telegram bot: {e}")
    
    def run_scheduler(self):
        """Run the scheduler in a separate thread"""
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    async def run(self):
        """Main run method"""
        try:
            # Validate configuration
            config.validate_config()
            logger.info("Configuration validated successfully")
            
            # Set running flag
            self.running = True
            
            # Schedule daily checks
            self.schedule_daily_check()
            
            # Start Telegram bot
            await self.start_bot()

            # Start price tracking if available
            if PRICE_TRACKING_AVAILABLE and config.PRICE_TRACKING_ENABLED:
                asyncio.create_task(price_tracker.start_tracking())
                logger.info("Price tracking started")

            # Send startup notification
            enhanced_status = " (Enhanced features enabled)" if PRICE_TRACKING_AVAILABLE else ""
            startup_message = f"🚀 Whale Tracker started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}{enhanced_status}\n\nDaily digest scheduled for {config.DAILY_CHECK_TIME} UTC\nUse /check for on-demand whale scanning"
            
            await self.telegram_bot.application.bot.send_message(
                chat_id=config.TELEGRAM_CHAT_ID,
                text=startup_message
            )
            
            logger.info("Whale Tracker started successfully")
            
            # Keep the application running
            while self.running:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            logger.error(f"Error in main run loop: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("Shutting down Whale Tracker...")
        self.running = False
        
        # Stop Telegram bot
        await self.stop_bot()
        
        # Send shutdown notification
        try:
            if self.telegram_bot.application:
                shutdown_message = f"🛑 Whale Tracker stopped at {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}"
                await self.telegram_bot.application.bot.send_message(
                    chat_id=config.TELEGRAM_CHAT_ID,
                    text=shutdown_message
                )
        except Exception as e:
            logger.error(f"Error sending shutdown notification: {e}")
        
        logger.info("Whale Tracker shutdown complete")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, initiating shutdown...")
    sys.exit(0)

async def test_components():
    """Test all components individually"""
    logger.info("Testing Whale Tracker components...")
    
    try:
        # Test configuration
        config.validate_config()
        logger.info("Configuration validation passed")
        
        # Test API handler
        api_handler = APIHandler()
        prices = api_handler.get_current_prices()
        logger.info(f"API handler test passed - ETH: ${prices['ETH']}, BTC: ${prices['BTC']}")
        
        # Test a small whale check
        transactions = api_handler.get_all_whale_transactions(hours_back=1)
        logger.info(f"Whale transaction check passed - Found {len(transactions)} transactions")
        
        # Test Google Sheets (if configured)
        try:
            sheets_handler = SheetsHandler()
            if sheets_handler.worksheet:
                logger.info("Google Sheets connection test passed")
            else:
                logger.warning("Google Sheets not properly configured")
        except Exception as e:
            logger.warning(f"Google Sheets test failed: {e}")

        # Test Telegram bot setup
        bot = PortfolioTrackerBot()
        bot.setup_bot()
        logger.info("Telegram bot setup test passed")

        logger.info("All component tests completed!")
        
    except Exception as e:
        logger.error(f"Component test failed: {e}")
        return False
    
    return True

async def main():
    """Main entry point"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Check if we're in test mode
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        success = await test_components()
        sys.exit(0 if success else 1)
    
    # Run the portfolio tracker
    tracker = PortfolioTracker()
    await tracker.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
