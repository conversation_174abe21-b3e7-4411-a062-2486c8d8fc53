# Crypto Whale Tracker Configuration
# Copy this file to .env and fill in your actual values

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# API Keys
ETHERSCAN_API_KEY=your_etherscan_api_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here_optional

# Multi-Coin API Keys (Optional - enable specific coins as needed)
BLOCKFROST_API_KEY=your_blockfrost_api_key_for_cardano_optional
HARMONY_API_KEY=your_harmony_api_key_optional
TONAPI_KEY=your_tonapi_key_optional

# Whale Tracking Configuration
WHALE_THRESHOLD_USD=500000

# Google Sheets Configuration
GOOGLE_SHEETS_ID=your_google_sheets_id_here
GOOGLE_SHEETS_CREDENTIALS_FILE=credentials.json

# Instructions:
# 1. Get Telegram Bot Token:
#    - Message @BotFather on Telegram
#    - Create a new bot with /newbot
#    - Copy the token provided
#
# 2. Get your Telegram Chat ID:
#    - Message your bot
#    - Visit: https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
#    - Find your chat ID in the response
#
# 3. Get Etherscan API Key:
#    - Visit https://etherscan.io/apis
#    - Create a free account
#    - Generate an API key
#
# 4. Get CoinGecko API Key (Optional):
#    - Visit https://www.coingecko.com/en/api
#    - Free tier works without key, but has lower rate limits
#
# 5. Setup Google Sheets:
#    - Create a Google Cloud Project
#    - Enable Google Sheets API
#    - Create a Service Account
#    - Download credentials.json
#    - Share your Google Sheet with the service account email
#    - Copy the Google Sheet ID from the URL
