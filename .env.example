# 🚀 Portfolio Tracker Configuration
# Copy this file to .env and fill in your actual values

# 🤖 Telegram Bot Configuration (REQUIRED)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# 🔑 Blockchain API Keys (REQUIRED)
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# 🔑 Exchange API Keys (for portfolio balances)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# 🔑 Multi-Chain API Keys (OPTIONAL - for specific coins)
BLOCKFROST_API_KEY=your_blockfrost_api_key_for_cardano_optional
COINGECKO_API_KEY=your_coingecko_api_key_optional

# 📊 Google Sheets Configuration (REQUIRED)
GOOGLE_SHEETS_ID=your_google_sheets_id_here

# 📄 Google Credentials (Choose ONE method)
# Method 1: Local file (for local development)
GOOGLE_SHEETS_CREDENTIALS_FILE=credentials.json

# Method 2: Base64 encoded (for Railway/cloud deployment)
# GOOGLE_CREDENTIALS_BASE64=your_base64_encoded_credentials_here

# Method 3: JSON string (alternative for cloud deployment)
# GOOGLE_CREDENTIALS_JSON={"type":"service_account","project_id":"..."}

# 💰 Portfolio Configuration
WHALE_THRESHOLD_USD=500000

# Instructions:
# 1. Get Telegram Bot Token:
#    - Message @BotFather on Telegram
#    - Create a new bot with /newbot
#    - Copy the token provided
#
# 2. Get your Telegram Chat ID:
#    - Message your bot
#    - Visit: https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
#    - Find your chat ID in the response
#
# 3. Get Etherscan API Key:
#    - Visit https://etherscan.io/apis
#    - Create a free account
#    - Generate an API key
#
# 4. Get CoinGecko API Key (Optional):
#    - Visit https://www.coingecko.com/en/api
#    - Free tier works without key, but has lower rate limits
#
# 5. Setup Google Sheets:
#    - Create a Google Cloud Project
#    - Enable Google Sheets API
#    - Create a Service Account
#    - Download credentials.json
#    - Share your Google Sheet with the service account email
#    - Copy the Google Sheet ID from the URL
